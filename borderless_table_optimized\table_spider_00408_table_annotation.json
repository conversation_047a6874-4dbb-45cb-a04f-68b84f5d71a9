{"table_ind": "table_spider_00408", "image_path": "table_spider_00408.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": true, "content": [{"bbox": null, "direction": null, "text": "Nutrition Facts", "score": null}], "bbox": {"p1": [117.0, 103.0], "p2": [199.0, 104.0], "p3": [199.0, 116.0], "p4": [117.0, 116.0]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "27 servings per container\nServing siae 1tbsp (12g)", "score": null}], "bbox": {"p1": [117.0, 116.0], "p2": [199.0, 116.0], "p3": [199.0, 134.0], "p4": [118.0, 134.0]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": false, "content": [{"bbox": null, "direction": null, "text": "###### ## #####\nCalories 0", "score": null}], "bbox": {"p1": [118.0, 134.0], "p2": [199.0, 134.0], "p3": [200.0, 154.0], "p4": [118.0, 154.0]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "% ##### #####", "score": null}], "bbox": {"p1": [118.0, 154.0], "p2": [200.0, 154.0], "p3": [200.0, 161.5], "p4": [118.0, 161.5]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [{"bbox": null, "direction": null, "text": "TEt## ### og 0%", "score": null}], "bbox": {"p1": [118.0, 161.5], "p2": [200.0, 161.5], "p3": [200.0, 169.0], "p4": [118.0, 169.0]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": false, "content": [{"bbox": null, "direction": null, "text": "##### #####0%", "score": null}], "bbox": {"p1": [118.0, 169.0], "p2": [200.0, 169.0], "p3": [200.0, 177.0], "p4": [118.0, 177.0]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": false, "content": [{"bbox": null, "direction": null, "text": "### ## ##", "score": null}], "bbox": {"p1": [118.0, 177.0], "p2": [200.0, 177.0], "p3": [200.0, 184.0], "p4": [119.0, 181.0]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": false, "content": [{"bbox": null, "direction": null, "text": "C##### ###  %", "score": null}], "bbox": {"p1": [119.0, 181.0], "p2": [200.0, 184.0], "p3": [200.0, 189.0], "p4": [119.0, 189.0]}, "lloc": {"start_row": 7, "end_row": 7, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": false, "content": [{"bbox": null, "direction": null, "text": "##### ###  %", "score": null}], "bbox": {"p1": [119.0, 189.0], "p2": [200.0, 189.0], "p3": [200.0, 197.0], "p4": [119.0, 197.0]}, "lloc": {"start_row": 8, "end_row": 8, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 9, "header": false, "content": [{"bbox": null, "direction": null, "text": "T### C######ate #g 0%", "score": null}], "bbox": {"p1": [119.0, 197.0], "p2": [200.0, 197.0], "p3": [200.0, 202.0], "p4": [119.0, 202.0]}, "lloc": {"start_row": 9, "end_row": 9, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 10, "header": false, "content": [{"bbox": null, "direction": null, "text": "O#### ### 0g 0%", "score": null}], "bbox": {"p1": [119.0, 202.0], "p2": [200.0, 202.0], "p3": [200.0, 210.0], "p4": [119.0, 210.0]}, "lloc": {"start_row": 10, "end_row": 10, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 11, "header": false, "content": [{"bbox": null, "direction": null, "text": "#### ##### og", "score": null}], "bbox": {"p1": [119.0, 210.0], "p2": [200.0, 210.0], "p3": [200.0, 217.04562043795625], "p4": [119.36496350364973, 217.04562043795625]}, "lloc": {"start_row": 11, "end_row": 11, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 12, "header": false, "content": [{"bbox": null, "direction": null, "text": "##### ##  0%", "score": null}], "bbox": {"p1": [119.36496350364973, 217.04562043795625], "p2": [200.0, 217.04562043795625], "p3": [200.17883211678827, 227.0], "p4": [120.27737226277372, 223.9087591240875]}, "lloc": {"start_row": 12, "end_row": 12, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 13, "header": false, "content": [{"bbox": null, "direction": null, "text": "###########\n#######", "score": null}], "bbox": {"p1": [120.27737226277372, 223.9087591240875], "p2": [200.17883211678827, 227.0], "p3": [201.0, 237.99999237060547], "p4": [120.66314407682773, 235.532486017603]}, "lloc": {"start_row": 13, "end_row": 13, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 14, "header": false, "content": [{"bbox": null, "direction": null, "text": "###########\n#######\n###########", "score": null}], "bbox": {"p1": [120.66314407682773, 235.532486017603], "p2": [201.0, 237.99999237060547], "p3": [200.53909301757812, 253.0059356689453], "p4": [119.95297241210938, 250.53077697753906]}, "lloc": {"start_row": 14, "end_row": 14, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}