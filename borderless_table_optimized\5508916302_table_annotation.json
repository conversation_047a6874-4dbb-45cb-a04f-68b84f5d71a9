{"table_ind": "5508916302", "image_path": "5508916302.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": true, "content": [{"bbox": null, "direction": null, "text": "Pins", "score": null}], "bbox": {"p1": [0.0, 31.137254901960112], "p2": [300.66608273686523, 36.44844873816455], "p3": [300.66608273686523, 93.065681971693], "p4": [0.0, 93.065681971693]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": true, "content": [{"bbox": null, "direction": null, "text": "24", "score": null}], "bbox": {"p1": [300.66608273686523, 36.44844873816455], "p2": [640.0, 37.143824027072604], "p3": [640.0, 93.065681971693], "p4": [300.66608273686523, 93.065681971693]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": false, "content": [{"bbox": null, "direction": null, "text": "Print Speed (cps)", "score": null}], "bbox": {"p1": [0.0, 93.065681971693], "p2": [300.66608273686523, 93.065681971693], "p3": [300.66608273686523, 149.91639158536952], "p4": [1.6886409736307542, 149.91639158536952]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "440 cps (High Speed Draft 10cpi)", "score": null}], "bbox": {"p1": [300.66608273686523, 93.065681971693], "p2": [640.0, 93.065681971693], "p3": [640.0, 149.91639158536952], "p4": [300.66608273686523, 149.91639158536952]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [{"bbox": null, "direction": null, "text": "Input Buffer", "score": null}], "bbox": {"p1": [1.6886409736307542, 149.91639158536952], "p2": [300.66608273686523, 149.91639158536952], "p3": [300.66608273686523, 205.55952998701275], "p4": [1.7647058823527004, 205.55952998701275]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": false, "content": [{"bbox": null, "direction": null, "text": "128KB", "score": null}], "bbox": {"p1": [300.66608273686523, 149.91639158536952], "p2": [640.0, 149.91639158536952], "p3": [640.0, 205.55952998701275], "p4": [300.66608273686523, 205.55952998701275]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": false, "content": [{"bbox": null, "direction": null, "text": "Paper Handling", "score": null}], "bbox": {"p1": [1.7647058823527004, 205.55952998701275], "p2": [300.66608273686523, 205.55952998701275], "p3": [300.66608273686523, 276.00895587592873], "p4": [3.7224736048267912, 272.340874811463]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": false, "content": [{"bbox": null, "direction": null, "text": "Paper Feeder (CSF): Optional (Single bin, High capacity)\nTractor Feeder: Push, Pull", "score": null}], "bbox": {"p1": [300.66608273686523, 205.55952998701275], "p2": [640.0, 205.55952998701275], "p3": [640.0, 276.00895587592873], "p4": [300.66608273686523, 276.00895587592873]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": false, "content": [{"bbox": null, "direction": null, "text": "Paper Path", "score": null}], "bbox": {"p1": [3.7224736048267912, 272.340874811463], "p2": [300.66608273686523, 276.00895587592873], "p3": [300.66608273686523, 355.863686118629], "p4": [6.4494382022471655, 355.863686118629]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 9, "header": false, "content": [{"bbox": null, "direction": null, "text": "Manual Insertion: Front/Rear in, Top out Push Tractor:\nFront/Rear in, Top out Pull Tractor: Front/Rear/Bottom\nin, Top out Cut Sheet Feeder: Front/Rear in, Top out", "score": null}], "bbox": {"p1": [300.66608273686523, 276.00895587592873], "p2": [640.0, 276.00895587592873], "p3": [640.0, 355.863686118629], "p4": [300.66608273686523, 355.863686118629]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 10, "header": false, "content": [{"bbox": null, "direction": null, "text": "Interfaces", "score": null}], "bbox": {"p1": [6.4494382022471655, 355.863686118629], "p2": [300.66608273686523, 355.863686118629], "p3": [302.1989343490369, 421.1718478889535], "p4": [9.23076923076933, 421.1718478889535]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 11, "header": false, "content": [{"bbox": null, "direction": null, "text": "Bi-directional parallel / USB (ver 1.1) Option: Type B I/F\nlevel 2", "score": null}], "bbox": {"p1": [300.66608273686523, 355.863686118629], "p2": [640.0, 355.863686118629], "p3": [640.0, 421.1718478889535], "p4": [302.1989343490369, 421.1718478889535]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 12, "header": false, "content": [{"bbox": null, "direction": null, "text": "MTBF (hrs at 25% duty cycle)", "score": null}], "bbox": {"p1": [9.23076923076933, 421.1718478889535], "p2": [302.1989343490369, 421.1718478889535], "p3": [302.1989343490369, 472.65709472892763], "p4": [11.323877068557977, 472.65709472892763]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 13, "header": false, "content": [{"bbox": null, "direction": null, "text": "20000 POH", "score": null}], "bbox": {"p1": [302.1989343490369, 421.1718478889535], "p2": [640.0, 421.1718478889535], "p3": [640.0, 475.5856269113152], "p4": [302.1989343490369, 472.65709472892763]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 14, "header": false, "content": [{"bbox": null, "direction": null, "text": "Copy Capability", "score": null}], "bbox": {"p1": [11.323877068557977, 472.65709472892763], "p2": [302.1989343490369, 472.65709472892763], "p3": [302.1989343490369, 524.190716440955], "p4": [12.914923291492505, 524.190716440955]}, "lloc": {"start_row": 7, "end_row": 7, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 15, "header": false, "content": [{"bbox": null, "direction": null, "text": "1 original + 4", "score": null}], "bbox": {"p1": [302.1989343490369, 472.65709472892763], "p2": [640.0, 475.5856269113152], "p3": [640.0, 527.3748547993265], "p4": [302.1989343490369, 524.190716440955]}, "lloc": {"start_row": 7, "end_row": 7, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 16, "header": false, "content": [{"bbox": null, "direction": null, "text": "Life of Printhead (million characters)", "score": null}], "bbox": {"p1": [12.914923291492505, 524.190716440955], "p2": [302.1989343490369, 524.190716440955], "p3": [302.1989343490369, 574.6459846385411], "p4": [15.514905149051902, 574.6459846385411]}, "lloc": {"start_row": 8, "end_row": 8, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 17, "header": false, "content": [{"bbox": null, "direction": null, "text": "400 million strokes/ wire", "score": null}], "bbox": {"p1": [302.1989343490369, 524.190716440955], "p2": [640.0, 527.3748547993265], "p3": [640.0, 577.6122579001018], "p4": [302.1989343490369, 574.6459846385411]}, "lloc": {"start_row": 8, "end_row": 8, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 18, "header": false, "content": [{"bbox": null, "direction": null, "text": "Black Ribbon Life (million characters)", "score": null}], "bbox": {"p1": [15.514905149051902, 574.6459846385411], "p2": [302.1989343490369, 574.6459846385411], "p3": [300.66608273686523, 625.4321895424839], "p4": [17.222222222222626, 627.222222222222]}, "lloc": {"start_row": 9, "end_row": 9, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 19, "header": false, "content": [{"bbox": null, "direction": null, "text": "Approx. 5 million characters", "score": null}], "bbox": {"p1": [302.1989343490369, 574.6459846385411], "p2": [640.0, 577.6122579001018], "p3": [640.0, 628.0297619047623], "p4": [300.66608273686523, 625.4321895424839]}, "lloc": {"start_row": 9, "end_row": 9, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}