# 表格标注优化器配置说明

## 🔧 配置方式

### 1. 代码内配置（推荐）

在 `table_annotation_optimizer.py` 文件的第 28-53 行修改 `CONFIG` 字典：

```python
CONFIG = {
    # 输入输出路径配置
    'input_dir': 'your_input_folder',           # 🔄 修改为你的输入文件夹路径
    'output_dir': 'your_output_folder',         # 🔄 修改为你的输出文件夹路径

    # 文件匹配模式
    'annotation_pattern': '*_table_annotation.json',  # 标注文件匹配模式
    'image_extensions': ['.jpg', '.jpeg', '.png', '.bmp', '.tiff'],  # 支持的图片格式

    # 算法参数
    'tolerance': 3.0,              # 🔄 基础容差阈值（像素）
    'preserve_perspective': True,   # 🔄 是否保持透视变换
    'adaptive_threshold': True,     # 🔄 是否使用自适应阈值

    # 处理参数
    'max_workers': 4,              # 🔄 并行处理线程数
}
```

### 2. 命令行配置

```bash
# 基本用法
python table_annotation_optimizer.py

# 自定义路径
python table_annotation_optimizer.py \
    --input-dir "D:/my_data/tables" \
    --output-dir "D:/my_output/optimized"

# 调整算法参数
python table_annotation_optimizer.py \
    --tolerance 5.0 \
    --workers 8 \
    --no-adaptive

# 强制矩形对齐
python table_annotation_optimizer.py \
    --no-perspective \
    --tolerance 2.0
```

## 📋 参数详解

### 路径配置

| 参数 | 说明 | 示例 |
|------|------|------|
| `input_dir` | 输入文件夹路径 | `"borderless_table"` |
| `output_dir` | 输出文件夹路径 | `"optimized_tables"` |
| `annotation_pattern` | 标注文件匹配模式 | `"*_annotation.json"` |
| `image_extensions` | 支持的图片格式 | `['.jpg', '.png']` |

### 算法参数

| 参数 | 说明 | 推荐值 | 范围 |
|------|------|--------|------|
| `tolerance` | 基础容差阈值（像素） | `3.0` | `1.0-10.0` |
| `preserve_perspective` | 保持透视变换 | `True` | `True/False` |
| `adaptive_threshold` | 自适应阈值 | `True` | `True/False` |

### 性能参数

| 参数 | 说明 | 推荐值 | 注意事项 |
|------|------|--------|----------|
| `max_workers` | 并行线程数 | `4` | 不超过CPU核心数 |

## 🎯 参数选择指南

### 1. 容差阈值 (tolerance)

```python
# 高精度表格（扫描质量好）
tolerance = 2.0

# 一般质量表格
tolerance = 3.0  # 默认推荐

# 低质量表格（手机拍照等）
tolerance = 5.0

# 极低质量表格
tolerance = 8.0
```

### 2. 透视变换 (preserve_perspective)

```python
# 保持自然透视效果（推荐）
preserve_perspective = True

# 强制矩形对齐（适合需要严格矩形的场景）
preserve_perspective = False
```

### 3. 自适应阈值 (adaptive_threshold)

```python
# 自动根据图像质量调整阈值（推荐）
adaptive_threshold = True

# 使用固定阈值（适合批量处理相似质量的图像）
adaptive_threshold = False
```

### 4. 并行线程数 (max_workers)

```python
# 根据CPU核心数设置
import os
max_workers = min(8, os.cpu_count())

# 内存受限时减少线程数
max_workers = 2

# 高性能服务器
max_workers = 16
```

## 📁 文件夹结构要求

```
your_input_folder/
├── image1.jpg
├── image1_table_annotation.json
├── image2.png
├── image2_table_annotation.json
└── ...
```

输出结构：
```
your_output_folder/
├── image1_table_annotation.json  # 优化后的标注
├── image2_table_annotation.json  # 优化后的标注
└── ...
```

## ⚡ 性能优化建议

### 1. 内存优化
```python
# 大批量处理时减少并行数
max_workers = 2

# 或者分批处理
# 每次处理100个文件
```

### 2. 速度优化
```python
# 关闭自适应阈值加快速度
adaptive_threshold = False

# 增加并行线程数
max_workers = 8
```

### 3. 质量优化
```python
# 启用自适应阈值
adaptive_threshold = True

# 保持透视变换
preserve_perspective = True

# 适当降低容差阈值
tolerance = 2.0
```

## 🔍 常见问题

### Q: 如何处理不同质量的图像？
A: 启用 `adaptive_threshold = True`，算法会自动根据图像分辨率调整阈值。

### Q: 处理速度太慢怎么办？
A: 增加 `max_workers` 或设置 `adaptive_threshold = False`。

### Q: 对齐效果不理想怎么办？
A: 调整 `tolerance` 参数，质量差的图像用更大的值。

### Q: 如何保持原始属性不变？
A: 算法自动保护所有原始属性，包括 `quality`、`type` 等。

### Q: 支持哪些文件格式？
A: 支持标准的JSON标注格式，图片支持jpg、png、bmp等常见格式。

## 📞 技术支持

如有问题，请检查：
1. 文件路径是否正确
2. 文件格式是否符合要求
3. 图片文件是否存在
4. 内存是否充足
