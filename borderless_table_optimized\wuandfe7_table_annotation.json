{"table_ind": "wuandfe7", "image_path": "wuandfe7.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": false, "content": [{"bbox": null, "direction": null, "text": "LOAD", "score": null}], "bbox": {"p1": [115.54937297202494, 127.4438557763062], "p2": [203.09595959595956, 127.4438557763062], "p3": [203.09595959595956, 147.57593615715177], "p4": [115.54937297202494, 147.57593615715177]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "STORE", "score": null}], "bbox": {"p1": [115.54937297202494, 147.57593615715177], "p2": [203.09595959595956, 147.57593615715177], "p3": [203.09595959595956, 168.08704726826272], "p4": [115.54937297202494, 168.08704726826272]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": false, "content": [{"bbox": null, "direction": null, "text": "IN", "score": null}], "bbox": {"p1": [118.4748984064654, 208.46207529843906], "p2": [203.09595959595956, 208.46207529843906], "p3": [205.52222222222207, 226.83333333333303], "p4": [118.4748984064654, 229.27548209366387]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "SUBTRACT", "score": null}], "bbox": {"p1": [118.4748984064654, 188.01034250084967], "p2": [203.09595959595956, 188.01034250084967], "p3": [203.09595959595956, 208.46207529843906], "p4": [118.4748984064654, 208.46207529843906]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [{"bbox": null, "direction": null, "text": "1111", "score": null}], "bbox": {"p1": [41.65289256198366, 248.30376492194665], "p2": [118.4748984064654, 248.30376492194665], "p3": [119.92837465564753, 267.72451790633613], "p4": [42.10192837465547, 269.27548209366387]}, "lloc": {"start_row": 7, "end_row": 7, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": false, "content": [{"bbox": null, "direction": null, "text": "Halt", "score": null}], "bbox": {"p1": [118.4748984064654, 248.30376492194665], "p2": [205.52222222222207, 248.30376492194665], "p3": [205.52222222222207, 267.0], "p4": [119.92837465564753, 267.72451790633613]}, "lloc": {"start_row": 7, "end_row": 7, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": false, "content": [{"bbox": null, "direction": null, "text": "OUT", "score": null}], "bbox": {"p1": [118.4748984064654, 229.27548209366387], "p2": [205.52222222222207, 226.83333333333303], "p3": [205.52222222222207, 248.30376492194665], "p4": [118.4748984064654, 248.30376492194665]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": false, "content": [{"bbox": null, "direction": null, "text": "0101", "score": null}], "bbox": {"p1": [38.826446280991604, 188.01034250084967], "p2": [118.4748984064654, 188.01034250084967], "p3": [118.4748984064654, 208.46207529843906], "p4": [39.72451790633613, 208.46207529843906]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": false, "content": [{"bbox": null, "direction": null, "text": "1101", "score": null}], "bbox": {"p1": [39.72451790633613, 208.46207529843906], "p2": [118.4748984064654, 208.46207529843906], "p3": [118.4748984064654, 229.27548209366387], "p4": [41.27548209366387, 229.27548209366387]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 9, "header": false, "content": [{"bbox": null, "direction": null, "text": "0000", "score": null}], "bbox": {"p1": [35.0, 127.4438557763062], "p2": [115.54937297202494, 127.4438557763062], "p3": [115.54937297202494, 147.57593615715177], "p4": [36.0, 147.57593615715177]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 10, "header": true, "content": [{"bbox": null, "direction": null, "text": "Mnemonic", "score": null}], "bbox": {"p1": [115.54937297202494, 107.0], "p2": [203.09595959595956, 106.39477557027203], "p3": [203.09595959595956, 127.4438557763062], "p4": [115.54937297202494, 127.4438557763062]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 11, "header": false, "content": [{"bbox": null, "direction": null, "text": "ADD", "score": null}], "bbox": {"p1": [115.54937297202494, 168.08704726826272], "p2": [203.09595959595956, 168.08704726826272], "p3": [203.09595959595956, 188.01034250084967], "p4": [118.4748984064654, 188.01034250084967]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 12, "header": false, "content": [{"bbox": null, "direction": null, "text": "Add value from memory location from Register", "score": null}], "bbox": {"p1": [203.09595959595956, 168.08704726826272], "p2": [699.9487666034156, 163.0], "p3": [700.0, 184.18975332068294], "p4": [203.09595959595956, 188.01034250084967]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 13, "header": false, "content": [{"bbox": null, "direction": null, "text": "0001", "score": null}], "bbox": {"p1": [36.0, 147.57593615715177], "p2": [115.54937297202494, 147.57593615715177], "p3": [115.54937297202494, 168.08704726826272], "p4": [37.55248618784526, 168.08704726826272]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 14, "header": false, "content": [{"bbox": null, "direction": null, "text": "1110", "score": null}], "bbox": {"p1": [41.27548209366387, 229.27548209366387], "p2": [118.4748984064654, 229.27548209366387], "p3": [118.4748984064654, 248.30376492194665], "p4": [41.65289256198366, 248.30376492194665]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 15, "header": false, "content": [{"bbox": null, "direction": null, "text": "0011", "score": null}], "bbox": {"p1": [37.55248618784526, 168.08704726826272], "p2": [115.54937297202494, 168.08704726826272], "p3": [118.4748984064654, 188.01034250084967], "p4": [38.826446280991604, 188.01034250084967]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 16, "header": false, "content": [{"bbox": null, "direction": null, "text": "Stop program", "score": null}], "bbox": {"p1": [205.52222222222207, 248.30376492194665], "p2": [699.8691588785045, 243.233644859813], "p3": [699.700934579439, 262.467289719626], "p4": [205.52222222222207, 267.0]}, "lloc": {"start_row": 7, "end_row": 7, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 17, "header": false, "content": [{"bbox": null, "direction": null, "text": "Output value from memory location or Register", "score": null}], "bbox": {"p1": [205.52222222222207, 226.83333333333303], "p2": [699.0, 223.5], "p3": [699.8691588785045, 243.233644859813], "p4": [205.52222222222207, 248.30376492194665]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 18, "header": true, "content": [{"bbox": null, "direction": null, "text": "Binary", "score": null}], "bbox": {"p1": [34.0, 107.0], "p2": [115.54937297202494, 107.0], "p3": [115.54937297202494, 127.4438557763062], "p4": [35.0, 127.4438557763062]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 19, "header": false, "content": [{"bbox": null, "direction": null, "text": "Input value and place in memory location or Register", "score": null}], "bbox": {"p1": [203.09595959595956, 208.46207529843906], "p2": [699.803738317757, 204.233644859813], "p3": [699.0, 223.5], "p4": [205.52222222222207, 226.83333333333303]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 20, "header": false, "content": [{"bbox": null, "direction": null, "text": "load value from memory location into Register", "score": null}], "bbox": {"p1": [203.09595959595956, 127.4438557763062], "p2": [700.0, 123.22720247295229], "p3": [700.0, 143.53632148377164], "p4": [203.09595959595956, 147.57593615715177]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 21, "header": false, "content": [{"bbox": null, "direction": null, "text": "Subtract value in memory location from Register", "score": null}], "bbox": {"p1": [203.09595959595956, 188.01034250084967], "p2": [700.0, 184.18975332068294], "p3": [699.803738317757, 204.233644859813], "p4": [203.09595959595956, 208.46207529843906]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 22, "header": false, "content": [{"bbox": null, "direction": null, "text": "Store value from Register into memory location", "score": null}], "bbox": {"p1": [203.09595959595956, 147.57593615715177], "p2": [700.0, 143.53632148377164], "p3": [699.9487666034156, 163.0], "p4": [203.09595959595956, 168.08704726826272]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 23, "header": true, "content": [{"bbox": null, "direction": null, "text": "Short Explanation", "score": null}], "bbox": {"p1": [203.09595959595956, 106.39477557027203], "p2": [699.6182380216383, 104.0], "p3": [700.0, 123.22720247295229], "p4": [203.09595959595956, 127.4438557763062]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}