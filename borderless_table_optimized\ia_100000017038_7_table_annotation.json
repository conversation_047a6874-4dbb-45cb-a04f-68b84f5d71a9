{"table_ind": "ia_100000017038_7", "image_path": "ia_100000017038_7.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": false, "content": [{"bbox": null, "direction": null, "text": "\"Take a bigger breath before\nyou start talking. We want\nyou to have the most air to\npower your speech\"", "score": null}], "bbox": {"p1": [111.40916079447915, 294.90097841229334], "p2": [322.0463147778517, 294.90097841229334], "p3": [322.0463147778517, 373.70502645502665], "p4": [111.40916079447915, 377.6270392671504]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "\"I want you to sit as upright\nas possible to make it easy\nfor your air to come in and\nout while speaking.\"", "score": null}], "bbox": {"p1": [111.40916079447915, 151.05941842000783], "p2": [318.8636363636363, 151.05941842000783], "p3": [322.0463147778517, 230.42222222222236], "p4": [111.40916079447915, 230.42222222222236]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": true, "content": [{"bbox": null, "direction": null, "text": "Instruction", "score": null}], "bbox": {"p1": [111.40916079447915, 80.0], "p2": [318.8636363636363, 79.09090909090901], "p3": [318.8636363636363, 151.05941842000783], "p4": [111.40916079447915, 151.05941842000783]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "\"Instead of trying to say all\nthe words in 1 breath, let's see\nif you can try to say just 1\nphrase per breath, to avoid\nrunning out of air.\"", "score": null}], "bbox": {"p1": [111.40916079447915, 581.0670926517572], "p2": [326.3569763569765, 575.5516212182878], "p3": [328.8834498834499, 678.6993006993007], "p4": [111.40916079447915, 683.7944535073411]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [{"bbox": null, "direction": null, "text": "\"Let's practice turning on your\nvoice as soon as you start\nbreathing out. We want your\nvoice and your air to work\ntogether.\"", "score": null}], "bbox": {"p1": [111.40916079447915, 377.6270392671504], "p2": [322.0463147778517, 373.70502645502665], "p3": [326.3569763569765, 473.3484848484848], "p4": [111.40916079447915, 478.2916666666667]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": false, "content": [{"bbox": null, "direction": null, "text": "\"Instead of taking a lot of\nbreaths in a sentence, let's\nsee if you can say more\nwords in 1 breath, so it sounds\nless choppy.\"", "score": null}], "bbox": {"p1": [111.40916079447915, 478.2916666666667], "p2": [326.3569763569765, 473.3484848484848], "p3": [326.3569763569765, 575.5516212182878], "p4": [111.40916079447915, 581.0670926517572]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": false, "content": [{"bbox": null, "direction": null, "text": "\"Let's practice taking a breath\nat the natural phrase breaks\nin a sentence.\"", "score": null}], "bbox": {"p1": [111.40916079447915, 230.42222222222236], "p2": [322.0463147778517, 230.42222222222236], "p3": [322.0463147778517, 294.90097841229334], "p4": [111.40916079447915, 294.90097841229334]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": false, "content": [{"bbox": null, "direction": null, "text": "Flaccid\nSpastic\nAtaxic\nHypokinetic", "score": null}], "bbox": {"p1": [322.0463147778517, 230.42222222222236], "p2": [419.47058823529414, 227.29411764705856], "p3": [421.2314814814813, 291.46296296296305], "p4": [322.0463147778517, 294.90097841229334]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": true, "content": [{"bbox": null, "direction": null, "text": "Typically\nBeneficial for\nDysarthria\nTypes:", "score": null}], "bbox": {"p1": [318.8636363636363, 79.09090909090901], "p2": [415.5, 78.27272727272748], "p3": [417.6011644832606, 148.87336244541484], "p4": [318.8636363636363, 151.05941842000783]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 9, "header": false, "content": [{"bbox": null, "direction": null, "text": "Flaccid\nSpastic\nHyperkinetic", "score": null}], "bbox": {"p1": [318.8636363636363, 151.05941842000783], "p2": [417.6011644832606, 148.87336244541484], "p3": [419.47058823529414, 227.29411764705856], "p4": [322.0463147778517, 230.42222222222236]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 10, "header": false, "content": [{"bbox": null, "direction": null, "text": "Flaccid\nSpastic\nHypokinetic", "score": null}], "bbox": {"p1": [322.0463147778517, 294.90097841229334], "p2": [421.2314814814813, 291.46296296296305], "p3": [423.0416666666665, 373.70502645502665], "p4": [322.0463147778517, 373.70502645502665]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 11, "header": false, "content": [{"bbox": null, "direction": null, "text": "Flaccid\nSpastic\nAtaxic\nHypoklnetic", "score": null}], "bbox": {"p1": [326.3569763569765, 473.3484848484848], "p2": [426.318181818182, 473.3484848484848], "p3": [429.5555555555552, 575.5516212182878], "p4": [326.3569763569765, 575.5516212182878]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 12, "header": false, "content": [{"bbox": null, "direction": null, "text": "Flaccid\nSpastic\nAtaxic\nHypoklnetic", "score": null}], "bbox": {"p1": [322.0463147778517, 373.70502645502665], "p2": [423.0416666666665, 373.70502645502665], "p3": [426.318181818182, 473.3484848484848], "p4": [326.3569763569765, 473.3484848484848]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 13, "header": false, "content": [{"bbox": null, "direction": null, "text": "Flaccid\nSpastic\nAtaxic\nHypoklnetic", "score": null}], "bbox": {"p1": [326.3569763569765, 575.5516212182878], "p2": [429.5555555555552, 575.5516212182878], "p3": [432.1428571428569, 678.1428571428569], "p4": [328.8834498834499, 678.6993006993007]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 14, "header": false, "content": [{"bbox": null, "direction": null, "text": "th an upright\nre.", "score": null}], "bbox": {"p1": [0.2840909090909918, 151.05941842000783], "p2": [111.40916079447915, 151.05941842000783], "p3": [111.40916079447915, 230.42222222222236], "p4": [0.18518518518521887, 230.42222222222236]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 15, "header": false, "content": [{"bbox": null, "direction": null, "text": "phonation at\neginning of\neto reduce air", "score": null}], "bbox": {"p1": [0.4488188976379206, 377.6270392671504], "p2": [111.40916079447915, 377.6270392671504], "p3": [111.40916079447915, 478.2916666666667], "p4": [0.125, 478.2916666666667]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 16, "header": false, "content": [{"bbox": null, "direction": null, "text": "le more deeply.", "score": null}], "bbox": {"p1": [0.27797833935028393, 294.90097841229334], "p2": [111.40916079447915, 294.90097841229334], "p3": [111.40916079447915, 377.6270392671504], "p4": [0.4488188976379206, 377.6270392671504]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 17, "header": false, "content": [{"bbox": null, "direction": null, "text": "breaths at\nal phrases.", "score": null}], "bbox": {"p1": [0.18518518518521887, 230.42222222222236], "p2": [111.40916079447915, 230.42222222222236], "p3": [111.40916079447915, 294.90097841229334], "p4": [0.27797833935028393, 294.90097841229334]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 18, "header": false, "content": [{"bbox": null, "direction": null, "text": "en the length\nases you are\nto say in a\n.", "score": null}], "bbox": {"p1": [0.5207667731629044, 581.0670926517572], "p2": [111.40916079447915, 581.0670926517572], "p3": [111.40916079447915, 683.7944535073411], "p4": [0.6737357259380587, 685.446982055465]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 19, "header": false, "content": [{"bbox": null, "direction": null, "text": "ase the length\nases you can\na breath.", "score": null}], "bbox": {"p1": [0.125, 478.2916666666667], "p2": [111.40916079447915, 478.2916666666667], "p3": [111.40916079447915, 581.0670926517572], "p4": [0.5207667731629044, 581.0670926517572]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 20, "header": true, "content": [{"bbox": null, "direction": null, "text": "egy", "score": null}], "bbox": {"p1": [1.0, 81.13636363636397], "p2": [111.40916079447915, 80.0], "p3": [111.40916079447915, 151.05941842000783], "p4": [0.2840909090909918, 151.05941842000783]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}