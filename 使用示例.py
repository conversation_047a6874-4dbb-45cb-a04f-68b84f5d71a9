#!/usr/bin/env python3
"""
表格标注优化器使用示例

这个脚本展示了如何使用表格标注优化器来处理表格标注文件。
"""

import os
from table_annotation_optimizer import PerspectiveAwareOptimizer, BatchProcessor

def example_single_file():
    """单文件处理示例"""
    print("=== 单文件处理示例 ===")
    
    # 创建优化器实例
    optimizer = PerspectiveAwareOptimizer(
        tolerance=3.0,              # 基础容差阈值
        preserve_perspective=True,   # 保持透视变换
        adaptive_threshold=True      # 使用自适应阈值
    )
    
    # 处理单个文件
    input_file = "borderless_table/monnpxwm_table_annotation.json"
    output_file = "output/monnpxwm_table_annotation_optimized.json"
    image_file = "borderless_table/monnpxwm.jpg"
    
    if os.path.exists(input_file):
        result = optimizer.optimize_table_annotation(
            annotation_file=input_file,
            output_file=output_file,
            image_file=image_file
        )
        
        if result['success']:
            print(f"✅ 处理成功!")
            print(f"   输入文件: {result['input_file']}")
            print(f"   输出文件: {result['output_file']}")
            print(f"   单元格数: {result['cell_count']}")
            if result['adaptive_threshold']:
                print(f"   自适应阈值: {result['adaptive_threshold']:.2f}")
        else:
            print(f"❌ 处理失败: {result['error']}")
    else:
        print(f"❌ 输入文件不存在: {input_file}")

def example_batch_processing():
    """批量处理示例"""
    print("\n=== 批量处理示例 ===")
    
    # 配置参数
    config = {
        'input_dir': 'borderless_table',           # 输入文件夹
        'output_dir': 'output_optimized',          # 输出文件夹
        'tolerance': 3.0,                          # 基础阈值
        'preserve_perspective': True,              # 保持透视
        'adaptive_threshold': True,                # 自适应阈值
        'max_workers': 4,                          # 并行线程数
        'annotation_pattern': '*_table_annotation.json',
        'image_extensions': ['.jpg', '.jpeg', '.png', '.bmp']
    }
    
    # 创建批量处理器
    processor = BatchProcessor(config)
    
    # 执行批量处理
    if os.path.exists(config['input_dir']):
        stats = processor.process_batch()
        
        print(f"\n📊 处理统计:")
        print(f"   总文件数: {stats['total_files']}")
        print(f"   成功处理: {stats['successful']}")
        print(f"   处理失败: {stats['failed']}")
        print(f"   成功率: {stats['successful']/stats['total_files']*100:.1f}%")
        print(f"   总耗时: {stats['total_time']:.1f}秒")
    else:
        print(f"❌ 输入目录不存在: {config['input_dir']}")

def example_custom_config():
    """自定义配置示例"""
    print("\n=== 自定义配置示例 ===")
    
    # 创建自定义优化器
    optimizer = PerspectiveAwareOptimizer(
        tolerance=5.0,               # 更大的容差阈值
        preserve_perspective=False,  # 不保持透视，强制矩形对齐
        adaptive_threshold=False     # 不使用自适应阈值
    )
    
    print("自定义配置:")
    print(f"  基础阈值: {optimizer.base_tolerance}")
    print(f"  保持透视: {optimizer.preserve_perspective}")
    print(f"  自适应阈值: {optimizer.adaptive_threshold}")

def main():
    """主函数"""
    print("🔧 表格标注优化器使用示例")
    print("=" * 50)
    
    # 确保输出目录存在
    os.makedirs("output", exist_ok=True)
    os.makedirs("output_optimized", exist_ok=True)
    
    # 运行示例
    example_single_file()
    example_batch_processing()
    example_custom_config()
    
    print("\n" + "=" * 50)
    print("✅ 示例运行完成!")
    print("\n💡 提示:")
    print("1. 修改配置区域的路径来处理你的数据")
    print("2. 使用命令行参数来快速调整参数")
    print("3. 查看输出文件验证优化效果")

if __name__ == "__main__":
    main()
