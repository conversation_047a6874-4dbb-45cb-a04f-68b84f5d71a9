{"table_ind": "ia_100000009501_5", "image_path": "ia_100000009501_5.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": true, "content": [{"bbox": null, "direction": null, "text": "Alvin's Solution", "score": null}], "bbox": {"p1": [59.356327056884766, 137.53244018554688], "p2": [524.7333780924479, 108.16922760009766], "p3": [529.0191955566406, 164.97021484375], "p4": [62.93817138671875, 194.33343505859375]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "∴The horizontal distance\nbetween the airplane\nand the airport is 839.1m.", "score": null}], "bbox": {"p1": [23.117198944091797, 402.4571533203125], "p2": [524.7333780924479, 384.1807861328125], "p3": [529.0191955566406, 507.482421875], "p4": [27.773681640625, 526.339599609375]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": false, "content": [{"bbox": null, "direction": null, "text": "tan40=x/100\nx=100(tan40)\nx=100(0.8391)\nx=83.91", "score": null}], "bbox": {"p1": [28.65839385986328, 187.68182373046875], "p2": [524.7333780924479, 159.6000213623047], "p3": [537.56787109375, 384.1807861328125], "p4": [41.22613525390625, 413.02423095703125]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "∴The horizontal distance\nbeteen the airplane\nand the airport is 119.18m,", "score": null}], "bbox": {"p1": [514.6626586914062, 374.4229736328125], "p2": [1023.8800659179688, 336.8843994140625], "p3": [1033.41748046875, 466.261474609375], "p4": [524.7333780924479, 503.800048828125]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [{"bbox": null, "direction": null, "text": "tan40=100/x\nx=100/tan40\nx=100/0.8391\nx=119.18", "score": null}], "bbox": {"p1": [505.3320007324219, 138.564697265625], "p2": [1023.0307006835938, 97.43346405029297], "p3": [1042.498779296875, 342.46875], "p4": [524.7333780924479, 384.1807861328125]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": true, "content": [{"bbox": null, "direction": null, "text": "Ely<sup>,</sup>s Solution", "score": null}], "bbox": {"p1": [517.9921875, 92.62516784667969], "p2": [1022.439697265625, 42.557865142822266], "p3": [1029.04736328125, 109.13273620605469], "p4": [524.7333780924479, 159.6000213623047]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}