{"table_ind": "7000511939", "image_path": "7000511939.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": true, "content": [{"bbox": null, "direction": null, "text": "PRINCIPAL DIMENSIONS", "score": null}], "bbox": {"p1": [381.0375570277811, 592.0], "p2": [622.7305124370773, 592.166666666667], "p3": [622.7305124370773, 619.5157190567543], "p4": [381.0375570277811, 619.5157190567543]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "Manhole punch\nDepth of throat............\nMaximum diameter of man-\nhole.......... .........\nMaximum thickness of plates\nRivet hole punch\nDepth of throat.... ........\nMaximum diameter of holes..\nMaximum thickness of plate.", "score": null}], "bbox": {"p1": [44.8299674328191, 619.5157190567543], "p2": [279.0498492925913, 619.5157190567543], "p3": [279.0498492925913, 805.9434886259191], "p4": [43.590187072753906, 804.3802490234375]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": false, "content": [{"bbox": null, "direction": null, "text": "Shear\nDepth of throat........... .\nThickness of plate...........\nHorsepower required.........20\nSpace required\nLength...... . . ..........\nWidth.. ....... . ........\nHeight above floor line......", "score": null}], "bbox": {"p1": [381.0375570277811, 619.5157190567543], "p2": [622.7305124370773, 619.5157190567543], "p3": [622.7305124370773, 802.2911392405058], "p4": [381.0375570277811, 804.4413797993402]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": true, "content": [{"bbox": null, "direction": null, "text": "In.", "score": null}], "bbox": {"p1": [279.0498492925913, 592.294964028777], "p2": [330.4007876654938, 591.5698005698005], "p3": [330.4007876654938, 619.5157190567543], "p4": [279.0498492925913, 619.5157190567543]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": true, "content": [{"bbox": null, "direction": null, "text": "Mm.", "score": null}], "bbox": {"p1": [330.4007876654938, 591.5698005698005], "p2": [381.0375570277811, 592.0], "p3": [381.0375570277811, 619.5157190567543], "p4": [330.4007876654938, 619.5157190567543]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": true, "content": [{"bbox": null, "direction": null, "text": "In.", "score": null}], "bbox": {"p1": [622.7305124370773, 592.166666666667], "p2": [673.3017848630898, 592.4184100418415], "p3": [673.3017848630898, 619.5157190567543], "p4": [622.7305124370773, 619.5157190567543]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": true, "content": [{"bbox": null, "direction": null, "text": "PRINCIPAL DIMENSIONS", "score": null}], "bbox": {"p1": [45.0, 590.0], "p2": [279.0498492925913, 592.294964028777], "p3": [279.0498492925913, 619.5157190567543], "p4": [44.8299674328191, 619.5157190567543]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": false, "content": [{"bbox": null, "direction": null, "text": "49\n11/2\nFt. In.\n13-8\n15-6\n11-10", "score": null}], "bbox": {"p1": [622.7305124370773, 619.5157190567543], "p2": [673.3017848630898, 619.5157190567543], "p3": [673.3017848630898, 801.7974683544298], "p4": [622.7305124370773, 802.2911392405058]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": false, "content": [{"bbox": null, "direction": null, "text": "241/2\n22\n3/8\n49\n11/2\n11/2", "score": null}], "bbox": {"p1": [279.0498492925913, 619.5157190567543], "p2": [330.4007876654938, 619.5157190567543], "p3": [330.4007876654938, 805.8235294117649], "p4": [279.0498492925913, 805.9434886259191]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 9, "header": false, "content": [{"bbox": null, "direction": null, "text": "622\n559\n9.5\n1244\n38\n38", "score": null}], "bbox": {"p1": [330.4007876654938, 619.5157190567543], "p2": [381.0375570277811, 619.5157190567543], "p3": [381.0375570277811, 804.4413797993402], "p4": [330.4007876654938, 805.8235294117649]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 10, "header": true, "content": [{"bbox": null, "direction": null, "text": "Mm.", "score": null}], "bbox": {"p1": [673.3017848630898, 592.4184100418415], "p2": [718.6276150627614, 592.2552301255228], "p3": [718.0, 619.5157190567543], "p4": [673.3017848630898, 619.5157190567543]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 5, "end_col": 5}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 11, "header": false, "content": [{"bbox": null, "direction": null, "text": "1244\n38\nM.\n4.17\n4.72\n3.61", "score": null}], "bbox": {"p1": [673.3017848630898, 619.5157190567543], "p2": [718.0, 619.5157190567543], "p3": [716.7442455242967, 801.8132992327373], "p4": [673.3017848630898, 801.7974683544298]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 5, "end_col": 5}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}