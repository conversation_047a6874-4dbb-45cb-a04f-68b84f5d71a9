{"table_ind": "ia_100000002573_5", "image_path": "ia_100000002573_5.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": true, "content": [{"bbox": null, "direction": null, "text": "GRADES 4-8", "score": null}], "bbox": {"p1": [749.0, 46.81818181818198], "p2": [1023.0, 42.0], "p3": [1023.0, 89.0], "p4": [752.030303030303, 94.33001321840148]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": true, "content": [{"bbox": null, "direction": null, "text": "GRADES 1-3", "score": null}], "bbox": {"p1": [454.1090176803876, 47.5], "p2": [749.0, 46.81818181818198], "p3": [752.030303030303, 94.33001321840148], "p4": [454.1090176803876, 94.33001321840148]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": true, "content": [{"bbox": null, "direction": null, "text": "KINDERGARTEN", "score": null}], "bbox": {"p1": [152.29701686121885, 43.61089494163434], "p2": [454.1090176803876, 47.5], "p3": [454.1090176803876, 94.33001321840148], "p4": [152.29701686121885, 91.27626459143949]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": true, "content": [{"bbox": null, "direction": null, "text": "COMPONENT", "score": null}], "bbox": {"p1": [5.0, 46.0], "p2": [152.29701686121885, 43.61089494163434], "p3": [152.29701686121885, 91.27626459143949], "p4": [4.945525291828744, 94.33001321840148]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [{"bbox": null, "direction": null, "text": "Word\nKnowledge", "score": null}], "bbox": {"p1": [5.3125, 393.9526376275925], "p2": [157.12797619047524, 393.9526376275925], "p3": [159.78481012658267, 677.8481012658228], "p4": [7.227848101266318, 682.0]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": false, "content": [{"bbox": null, "direction": null, "text": "Expressiveness", "score": null}], "bbox": {"p1": [4.945525291828744, 94.33001321840148], "p2": [152.29701686121885, 91.27626459143949], "p3": [155.00297619047524, 300.60223028203353], "p4": [5.5625, 300.60223028203353]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": false, "content": [{"bbox": null, "direction": null, "text": "interactive read-alouds\nKWL charts\nLearning logs\nOpen-mind portraits\nPossible sentences\nQuestion-Answer-Relationships\nQuickwriting\nSemantic feature analysis\nTea party\nWord sorts\nWord walls", "score": null}], "bbox": {"p1": [757.3741496598623, 388.8639455782318], "p2": [1024.0, 383.3809523809523], "p3": [1024.0, 662.8431372549021], "p4": [764.8571428571422, 665.6050420168071]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": false, "content": [{"bbox": null, "direction": null, "text": "Book talks\nGrand conversations\nHot seat\nInteractive read-alouds\nPossible sentences\nQuickwriting\nShared reading\nTea party", "score": null}], "bbox": {"p1": [752.030303030303, 94.33001321840148], "p2": [1023.0, 89.0], "p3": [1024.0, 292.0], "p4": [754.8525641025635, 300.60223028203353]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": false, "content": [{"bbox": null, "direction": null, "text": "Wordplay", "score": null}], "bbox": {"p1": [5.5625, 300.60223028203353], "p2": [155.00297619047524, 300.60223028203353], "p3": [157.12797619047524, 393.9526376275925], "p4": [5.3125, 393.9526376275925]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 9, "header": false, "content": [{"bbox": null, "direction": null, "text": "Interactive read-alouds\nInteractive writing\nKWL charts.", "score": null}], "bbox": {"p1": [157.12797619047524, 393.9526376275925], "p2": [460.09197206734007, 393.9526376275925], "p3": [466.0875683606773, 672.5363478724826], "p4": [159.78481012658267, 677.8481012658228]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 10, "header": false, "content": [{"bbox": null, "direction": null, "text": "Grand conversations\nInteractive read-afouds\nInteractive writing\nLanguage Experience Approach\nStory boards\nStory retelling", "score": null}], "bbox": {"p1": [152.29701686121885, 91.27626459143949], "p2": [454.1090176803876, 94.33001321840148], "p3": [460.09197206734007, 300.60223028203353], "p4": [155.00297619047524, 300.60223028203353]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 11, "header": false, "content": [{"bbox": null, "direction": null, "text": "Book talks\nGrand conversations\ninteractive read-alouds\ninteractive writing\nShared reading\nStory boards\nStory retelling\nTea party", "score": null}], "bbox": {"p1": [454.1090176803876, 94.33001321840148], "p2": [752.030303030303, 94.33001321840148], "p3": [754.8525641025635, 300.60223028203353], "p4": [460.09197206734007, 300.60223028203353]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 12, "header": false, "content": [{"bbox": null, "direction": null, "text": "Choral reading", "score": null}], "bbox": {"p1": [754.8525641025635, 300.60223028203353], "p2": [1024.0, 292.0], "p3": [1024.0, 383.3809523809523], "p4": [757.3741496598623, 388.8639455782318]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 13, "header": false, "content": [{"bbox": null, "direction": null, "text": "Interactive read-alouds\nShared reading", "score": null}], "bbox": {"p1": [155.00297619047524, 300.60223028203353], "p2": [460.09197206734007, 300.60223028203353], "p3": [460.09197206734007, 393.9526376275925], "p4": [157.12797619047524, 393.9526376275925]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 14, "header": false, "content": [{"bbox": null, "direction": null, "text": "Choral reading\nInteractive read-alouds\nShared reading", "score": null}], "bbox": {"p1": [460.09197206734007, 300.60223028203353], "p2": [754.8525641025635, 300.60223028203353], "p3": [757.3741496598623, 388.8639455782318], "p4": [460.09197206734007, 393.9526376275925]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 15, "header": false, "content": [{"bbox": null, "direction": null, "text": "interactive read-alouds\nKWL charts\nSemantic feature analysis\nTea party\nWord sorts\nWord walls", "score": null}], "bbox": {"p1": [460.09197206734007, 393.9526376275925], "p2": [757.3741496598623, 388.8639455782318], "p3": [764.8571428571422, 665.6050420168071], "p4": [466.0875683606773, 672.5363478724826]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}