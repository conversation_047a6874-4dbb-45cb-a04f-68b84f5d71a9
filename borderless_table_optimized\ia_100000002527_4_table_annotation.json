{"table_ind": "ia_100000002527_4", "image_path": "ia_100000002527_4.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": false, "content": [{"bbox": null, "direction": null, "text": "Calculate the 20% increased invoice amount and call it\nIncreasedAmount. Sort by invoice number, and xisitiD.\nShow all.", "score": null}], "bbox": {"p1": [112.87951807228933, 150.690763052209], "p2": [412.47572855780794, 153.6320208381203], "p3": [409.24758339835836, 217.37255141314336], "p4": [115.48192771084359, 213.14056224899605]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "Show the patient lastname, firstname in one column\nand call it FullName and patient email in another\ncolumn so that we can send them emails about a new\npreventive medicine program.(...＆〝,〞＆...)", "score": null}], "bbox": {"p1": [115.48192771084359, 213.14056224899605], "p2": [409.24758339835836, 217.37255141314336], "p3": [409.24758339835836, 279.0], "p4": [118.7048192771083, 275.8032128514057]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": false, "content": [{"bbox": null, "direction": null, "text": "What is the maximum, minimum, total, and average\ninvoice amount in our facility?", "score": null}], "bbox": {"p1": [110.52634724946283, 57.11084337349403], "p2": [414.67183462532284, 57.11084337349403], "p3": [412.47572855780794, 103.0], "p4": [110.52634724946283, 100.9196787148594]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [], "bbox": {"p1": [407.48743995590195, 342.86274509803934], "p2": [699.7462686567164, 342.86274509803934], "p3": [699.7906976744184, 388.97647058823514], "p4": [407.48743995590195, 388.97647058823514]}, "lloc": {"start_row": 9, "end_row": 9, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [], "bbox": {"p1": [409.24758339835836, 153.6320208381203], "p2": [699.1041259765625, 153.6320208381203], "p3": [699.0, 217.37255141314336], "p4": [409.24758339835836, 217.37255141314336]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": false, "content": [{"bbox": null, "direction": null, "text": "Show the number of patients in each city and zip code.\nSort by city and zip.", "score": null}], "bbox": {"p1": [120.54216867469904, 339.309236947791], "p2": [407.48743995590195, 342.86274509803934], "p3": [407.48743995590195, 388.97647058823514], "p4": [123.92771084337353, 385.69778272754877]}, "lloc": {"start_row": 9, "end_row": 9, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": false, "content": [], "bbox": {"p1": [409.24758339835836, 217.37255141314336], "p2": [698.0, 217.37255141314336], "p3": [699.0, 281.0], "p4": [409.24758339835836, 279.0]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": false, "content": [{"bbox": null, "direction": null, "text": "Show the number of patients in every city and zip\ncode where the number of patients is greater than 3.\nSort by city and zip.", "score": null}], "bbox": {"p1": [123.92771084337353, 385.69778272754877], "p2": [409.24758339835836, 385.69778272754877], "p3": [407.48743995590195, 433.6806951307533], "p4": [123.92771084337353, 430.6867469879521]}, "lloc": {"start_row": 10, "end_row": 10, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": false, "content": [], "bbox": {"p1": [414.67183462532284, 57.11084337349403], "p2": [699.0, 60.0], "p3": [699.0, 105.0], "p4": [412.47572855780794, 103.0]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 9, "header": false, "content": [{"bbox": null, "direction": null, "text": "27", "score": null}], "bbox": {"p1": [57.0, 126.13654618473902], "p2": [112.87951807228933, 126.13654618473902], "p3": [112.87951807228933, 150.690763052209], "p4": [58.722891566264934, 150.690763052209]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 10, "header": false, "content": [{"bbox": null, "direction": null, "text": "Show the number of visits for each invoice item where\\ the number of visits is greater than 2.", "score": null}], "bbox": {"p1": [118.7048192771083, 299.0], "p2": [409.24758339835836, 302.941176470588], "p3": [407.48743995590195, 342.86274509803934], "p4": [120.54216867469904, 339.309236947791]}, "lloc": {"start_row": 8, "end_row": 8, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 11, "header": false, "content": [], "bbox": {"p1": [407.48743995590195, 388.97647058823514], "p2": [699.7906976744184, 388.97647058823514], "p3": [699.0, 435.0], "p4": [407.48743995590195, 433.6806951307533]}, "lloc": {"start_row": 10, "end_row": 10, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 12, "header": false, "content": [{"bbox": null, "direction": null, "text": "31", "score": null}], "bbox": {"p1": [65.27710843373507, 299.0], "p2": [118.7048192771083, 299.0], "p3": [120.54216867469904, 339.309236947791], "p4": [67.2409638554218, 339.309236947791]}, "lloc": {"start_row": 8, "end_row": 8, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 13, "header": false, "content": [{"bbox": null, "direction": null, "text": "32", "score": null}], "bbox": {"p1": [67.2409638554218, 339.309236947791], "p2": [120.54216867469904, 339.309236947791], "p3": [123.92771084337353, 385.69778272754877], "p4": [69.72289156626493, 385.69778272754877]}, "lloc": {"start_row": 9, "end_row": 9, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 14, "header": false, "content": [{"bbox": null, "direction": null, "text": "What is the total amount paid by insurance?", "score": null}], "bbox": {"p1": [110.52634724946283, 31.331117819308663], "p2": [414.67183462532284, 33.646010337257884], "p3": [414.67183462532284, 57.11084337349403], "p4": [110.52634724946283, 57.11084337349403]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 15, "header": false, "content": [{"bbox": null, "direction": null, "text": "From how many unique cities are our patients?", "score": null}], "bbox": {"p1": [112.87951807228933, 126.13654618473902], "p2": [412.47572855780794, 128.0], "p3": [412.47572855780794, 153.6320208381203], "p4": [112.87951807228933, 150.690763052209]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 16, "header": false, "content": [{"bbox": null, "direction": null, "text": "26", "score": null}], "bbox": {"p1": [55.96385542168673, 100.9196787148594], "p2": [110.52634724946283, 100.9196787148594], "p3": [112.87951807228933, 126.13654618473902], "p4": [57.0, 126.13654618473902]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 17, "header": false, "content": [], "bbox": {"p1": [409.24758339835836, 302.941176470588], "p2": [699.0, 302.941176470588], "p3": [699.7462686567164, 342.86274509803934], "p4": [407.48743995590195, 342.86274509803934]}, "lloc": {"start_row": 8, "end_row": 8, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 18, "header": false, "content": [{"bbox": null, "direction": null, "text": "How many billing records we have in the billing table.", "score": null}], "bbox": {"p1": [110.52634724946283, 100.9196787148594], "p2": [412.47572855780794, 103.0], "p3": [412.47572855780794, 128.0], "p4": [112.87951807228933, 126.13654618473902]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 19, "header": false, "content": [{"bbox": null, "direction": null, "text": "33", "score": null}], "bbox": {"p1": [69.72289156626493, 385.69778272754877], "p2": [123.92771084337353, 385.69778272754877], "p3": [123.92771084337353, 430.6867469879521], "p4": [72.0, 430.0]}, "lloc": {"start_row": 10, "end_row": 10, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 20, "header": false, "content": [{"bbox": null, "direction": null, "text": "28", "score": null}], "bbox": {"p1": [58.722891566264934, 150.690763052209], "p2": [112.87951807228933, 150.690763052209], "p3": [115.48192771084359, 213.14056224899605], "p4": [61.24096385542134, 213.14056224899605]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 21, "header": false, "content": [{"bbox": null, "direction": null, "text": "30", "score": null}], "bbox": {"p1": [64.0, 275.8032128514057], "p2": [118.7048192771083, 275.8032128514057], "p3": [118.7048192771083, 299.0], "p4": [65.27710843373507, 299.0]}, "lloc": {"start_row": 7, "end_row": 7, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 22, "header": false, "content": [{"bbox": null, "direction": null, "text": "29", "score": null}], "bbox": {"p1": [61.24096385542134, 213.14056224899605], "p2": [115.48192771084359, 213.14056224899605], "p3": [118.7048192771083, 275.8032128514057], "p4": [64.0, 275.8032128514057]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 23, "header": false, "content": [{"bbox": null, "direction": null, "text": "24", "score": null}], "bbox": {"p1": [53.02230373648695, 31.331117819308663], "p2": [110.52634724946283, 31.331117819308663], "p3": [110.52634724946283, 57.11084337349403], "p4": [54.48192771084314, 57.11084337349403]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 24, "header": false, "content": [{"bbox": null, "direction": null, "text": "25", "score": null}], "bbox": {"p1": [54.48192771084314, 57.11084337349403], "p2": [110.52634724946283, 57.11084337349403], "p3": [110.52634724946283, 100.9196787148594], "p4": [55.96385542168673, 100.9196787148594]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 25, "header": false, "content": [], "bbox": {"p1": [414.67183462532284, 33.646010337257884], "p2": [699.999755859375, 33.646010337257884], "p3": [699.0, 60.0], "p4": [414.67183462532284, 57.11084337349403]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 26, "header": false, "content": [], "bbox": {"p1": [412.47572855780794, 128.0], "p2": [699.0, 130.0], "p3": [699.1041259765625, 153.6320208381203], "p4": [412.47572855780794, 153.6320208381203]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 27, "header": false, "content": [], "bbox": {"p1": [412.47572855780794, 103.0], "p2": [699.0, 105.0], "p3": [699.0, 130.0], "p4": [412.47572855780794, 128.0]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 28, "header": false, "content": [{"bbox": null, "direction": null, "text": "How many visits were there for each invoice item?", "score": null}], "bbox": {"p1": [118.7048192771083, 275.8032128514057], "p2": [409.24758339835836, 279.0], "p3": [409.24758339835836, 302.941176470588], "p4": [118.7048192771083, 299.0]}, "lloc": {"start_row": 7, "end_row": 7, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 29, "header": false, "content": [], "bbox": {"p1": [409.24758339835836, 279.0], "p2": [699.0, 281.0], "p3": [699.0, 302.941176470588], "p4": [409.24758339835836, 302.941176470588]}, "lloc": {"start_row": 7, "end_row": 7, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 30, "header": true, "content": [{"bbox": null, "direction": null, "text": "Aggregate functions: sum(), count), avg(), max(), min()", "score": null}], "bbox": {"p1": [52.794647216796875, 5.985151290893555], "p2": [700.098388671875, 8.490253448486328], "p3": [699.999755859375, 33.646010337257884], "p4": [53.02230373648695, 31.331117819308663]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}