{"table_ind": 0, "image_path": "", "type": 1, "cells": [{"cell_ind": 0, "header": false, "content": [{"bbox": null, "direction": null, "text": "Manual absolute ON/OFF", "score": null}], "bbox": {"p1": [269.0446964095306, 297.17647058823513], "p2": [454.0776708814366, 294.22090261282665], "p3": [454.0776708814366, 376.04067695962], "p4": [269.0446964095306, 376.04067695962]}, "lloc": {"start_row": 7, "end_row": 7, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "itioaing and linear Interplolation\n3 aces sinultaneously\ncular interpolation...\n2 axes sinultaneously", "score": null}], "bbox": {"p1": [0.0, 56.25], "p2": [269.0446964095306, 56.25], "p3": [269.0446964095306, 129.0], "p4": [0.0, 129.0]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 0}, "width": null, "color": null}}, {"cell_ind": 2, "header": false, "content": [{"bbox": null, "direction": null, "text": "Manual jog feed", "score": null}], "bbox": {"p1": [269.0446964095306, 64.0], "p2": [454.0776708814366, 64.0], "p3": [454.0776708814366, 129.0], "p4": [269.0446964095306, 129.0]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "0 to 2000 nn/r\ndesired axis.\nrotary switch", "score": null}], "bbox": {"p1": [454.0776708814366, 64.0], "p2": [567.0, 64.0], "p3": [567.0, 129.0], "p4": [454.0776708814366, 129.0]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 0, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [{"bbox": null, "direction": null, "text": "100 pulses/rot\npulse generato\nHagnffication", "score": null}], "bbox": {"p1": [454.0776708814366, 179.0], "p2": [567.0, 179.0], "p3": [567.0, 245.50490196078454], "p4": [454.0776708814366, 245.50490196078454]}, "lloc": {"start_row": 4, "end_row": 5, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 0, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": false, "content": [{"bbox": null, "direction": null, "text": "Manual bandle feed", "score": null}], "bbox": {"p1": [269.0446964095306, 179.0], "p2": [454.0776708814366, 179.0], "p3": [454.0776708814366, 245.50490196078454], "p4": [269.0446964095306, 245.50490196078454]}, "lloc": {"start_row": 4, "end_row": 5, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": false, "content": [{"bbox": null, "direction": null, "text": "1d feed:\nLiear acceleration and decelera-\ntion\nting feed:\nExponential acceleration and\ndeceleration", "score": null}], "bbox": {"p1": [0.4789915966384797, 291.4558823529412], "p2": [269.0446964095306, 291.4558823529412], "p3": [269.0446964095306, 393.75], "p4": [0.0, 393.75]}, "lloc": {"start_row": 7, "end_row": 8, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 0}, "width": null, "color": null}}, {"cell_ind": 7, "header": false, "content": [{"bbox": null, "direction": null, "text": "G04 for dwell:", "score": null}], "bbox": {"p1": [454.0776708814366, 245.50490196078454], "p2": [567.0, 245.50490196078454], "p3": [566.2375296912114, 294.22090261282665], "p4": [454.0776708814366, 294.22090261282665]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 0, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": false, "content": [{"bbox": null, "direction": null, "text": "with nanual ab\nnenu switch,#\nvalue is rene#\nnanually.", "score": null}], "bbox": {"p1": [454.0776708814366, 294.22090261282665], "p2": [566.2375296912114, 294.22090261282665], "p3": [566.4750593824228, 376.04067695962], "p4": [454.0776708814366, 376.04067695962]}, "lloc": {"start_row": 7, "end_row": 7, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 0, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 9, "header": false, "content": [{"bbox": null, "direction": null, "text": "Incremental feed", "score": null}], "bbox": {"p1": [269.0446964095306, 129.0], "p2": [454.0776708814366, 129.0], "p3": [454.0776708814366, 179.0], "p4": [269.0446964095306, 179.0]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 10, "header": false, "content": [{"bbox": null, "direction": null, "text": "0.001 to 10 mm\nvariable in 5", "score": null}], "bbox": {"p1": [454.0776708814366, 129.0], "p2": [567.0, 129.0], "p3": [567.0, 179.0], "p4": [454.0776708814366, 179.0]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 0, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 11, "header": false, "content": [{"bbox": null, "direction": null, "text": "Exact stop check", "score": null}], "bbox": {"p1": [269.0446964095306, 30.598983270073436], "p2": [454.0776708814366, 30.598983270073436], "p3": [454.0776708814366, 64.0], "p4": [269.0446964095306, 64.0]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 12, "header": false, "content": [{"bbox": null, "direction": null, "text": "<PERSON><PERSON>", "score": null}], "bbox": {"p1": [269.0446964095306, 245.50490196078454], "p2": [454.0776708814366, 245.50490196078454], "p3": [454.0776708814366, 294.22090261282665], "p4": [269.0446964095306, 297.17647058823513]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 13, "header": false, "content": [{"bbox": null, "direction": null, "text": "G09 to prevent", "score": null}], "bbox": {"p1": [454.0776708814366, 30.598983270073436], "p2": [566.6884735202493, 30.598983270073436], "p3": [567.0, 64.0], "p4": [454.0776708814366, 64.0]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 0, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 14, "header": false, "content": [{"bbox": null, "direction": null, "text": "imua input increment...\n0.001 mm/0.0001 inch\nimum output increaent...\n0.001 mm", "score": null}], "bbox": {"p1": [0.0, 129.0], "p2": [269.0446964095306, 129.0], "p3": [269.0446964095306, 202.340336134454], "p4": [0.0, 202.340336134454]}, "lloc": {"start_row": 3, "end_row": 4, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 0}, "width": null, "color": null}}, {"cell_ind": 15, "header": true, "content": [{"bbox": null, "direction": null, "text": "0 to 200z (in", "score": null}], "bbox": {"p1": [454.0776708814366, 1.0], "p2": [567.0, 0.0], "p3": [566.6884735202493, 30.598983270073436], "p4": [454.0776708814366, 30.598983270073436]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 0, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 16, "header": false, "content": [{"bbox": null, "direction": null, "text": "Backlash offset", "score": null}], "bbox": {"p1": [269.0446964095306, 376.04067695962], "p2": [454.0776708814366, 376.04067695962], "p3": [454.0776708814366, 423.0], "p4": [269.0446964095306, 424.0]}, "lloc": {"start_row": 8, "end_row": 9, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 17, "header": false, "content": [{"bbox": null, "direction": null, "text": "atant tangential speed:\nMax. 5,000 na/min (197''/min)", "score": null}], "bbox": {"p1": [0.0, 245.50490196078454], "p2": [269.0446964095306, 245.50490196078454], "p3": [269.0446964095306, 291.4558823529412], "p4": [0.4789915966384797, 291.4558823529412]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 0}, "width": null, "color": null}}, {"cell_ind": 18, "header": false, "content": [{"bbox": null, "direction": null, "text": "Correction of\nnechanical sys", "score": null}], "bbox": {"p1": [454.0776708814366, 376.04067695962], "p2": [566.4750593824228, 376.04067695962], "p3": [567.0, 424.0], "p4": [454.0776708814366, 424.0]}, "lloc": {"start_row": 8, "end_row": 9, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 0, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 19, "header": true, "content": [{"bbox": null, "direction": null, "text": "Cutting feed override", "score": null}], "bbox": {"p1": [269.0446964095306, 1.0], "p2": [454.0776708814366, 0.0], "p3": [454.0776708814366, 30.598983270073436], "p4": [269.0446964095306, 32.11480362537736]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 20, "header": false, "content": [{"bbox": null, "direction": null, "text": "ee axes of x. y and z", "score": null}], "bbox": {"p1": [0.0, 25.885196374622296], "p2": [269.0446964095306, 25.885196374622296], "p3": [269.0446964095306, 56.25], "p4": [0.0, 56.25]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 0}, "width": null, "color": null}}, {"cell_ind": 21, "header": false, "content": [{"bbox": null, "direction": null, "text": "nd Y axea: 15 n/min (390''/min)\n12 n/min (472''/min)", "score": null}], "bbox": {"p1": [0.0, 202.340336134454], "p2": [269.0446964095306, 202.340336134454], "p3": [269.0446964095306, 245.50490196078454], "p4": [0.0, 245.50490196078454]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 0}, "width": null, "color": null}}, {"cell_ind": 22, "header": true, "content": [{"bbox": null, "direction": null, "text": "Specification", "score": null}], "bbox": {"p1": [0.0, 4.0], "p2": [269.0446964095306, 4.0], "p3": [269.0446964095306, 25.885196374622296], "p4": [0.0, 25.885196374622296]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 0}, "width": null, "color": null}}, {"cell_ind": 23, "header": false, "content": [{"bbox": null, "direction": null, "text": "nn/min (3.9''/min). 25Z, 50Z, 100t", "score": null}], "bbox": {"p1": [0.0, 393.75], "p2": [269.0446964095306, 393.75], "p3": [269.0446964095306, 424.0], "p4": [0.0, 422.0]}, "lloc": {"start_row": 9, "end_row": 9, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 0}, "width": null, "color": null}}]}