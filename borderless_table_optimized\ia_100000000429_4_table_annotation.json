{"table_ind": "ia_100000000429_4", "image_path": "ia_100000000429_4.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": false, "content": [{"bbox": null, "direction": null, "text": "1", "score": null}], "bbox": {"p1": [143.924342081923, 86.07379183606872], "p2": [182.05504587155943, 89.20663936443336], "p3": [177.43622888053642, 176.43614886149186], "p4": [136.29154518950418, 176.43614886149186]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "Construct a line chart to examine the \"Total\nSales\" growth (row 89 of the worksheet)\nacnoss each month in 2012. Format the chart\nwith the title \"Quickstove Product Sales\" and\na legend to the right of the chart. Use the\nmonths in row 6 of the worksheet as the\nlabels for the horizontal axis. Add a linear\ntrendline to the chart.", "score": null}], "bbox": {"p1": [216.36839811310628, 89.20663936443334], "p2": [422.13299560546875, 93.79792022705078], "p3": [420.947509765625, 180.90353393554688], "p4": [216.36839811310628, 176.4361488614919]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": false, "content": [{"bbox": null, "direction": null, "text": "0/12", "score": null}], "bbox": {"p1": [182.05504587155943, 89.20663936443336], "p2": [216.36839811310628, 89.20663936443334], "p3": [216.36839811310628, 176.4361488614919], "p4": [177.43622888053642, 176.43614886149186]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": true, "content": [{"bbox": null, "direction": null, "text": "Task #", "score": null}], "bbox": {"p1": [145.0, 70.0], "p2": [182.05504587155943, 72.09746588693952], "p3": [182.05504587155943, 89.20663936443336], "p4": [143.924342081923, 86.07379183606872]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": true, "content": [{"bbox": null, "direction": null, "text": "Points", "score": null}], "bbox": {"p1": [182.05504587155943, 72.09746588693952], "p2": [216.36839811310628, 75.29240091781173], "p3": [216.36839811310628, 89.20663936443334], "p4": [182.05504587155943, 89.20663936443336]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": true, "content": [{"bbox": null, "direction": null, "text": "Task Description", "score": null}], "bbox": {"p1": [216.36839811310628, 75.29240091781173], "p2": [405.60723876953125, 78.05279541015625], "p3": [405.3616943359375, 93.79792022705078], "p4": [216.36839811310628, 92.07546997070312]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": false, "content": [{"bbox": null, "direction": null, "text": "0/15", "score": null}], "bbox": {"p1": [177.43622888053642, 176.43614886149186], "p2": [216.36839811310628, 176.4361488614919], "p3": [216.36839811310628, 297.4398272321224], "p4": [170.83486238532123, 295.9174311926606]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": false, "content": [{"bbox": null, "direction": null, "text": "Construct a line chart to compare the sales\ntrends for the following products and kits:\nFirebox Deluxe Combo Kit, Firebox Master\nCombo Kit, Folding Firebox Campfire Stove,\nand Quickstove Fuel Disk. Format the chart\nwith the title \"Sales For Selected Products\"\nand a legend to the right of the chart. Use\nthe months in row 6 of the worksheet as the\nlabels for the horizontal axis.", "score": null}], "bbox": {"p1": [212.38766479492188, 176.43614886149186], "p2": [442.0332336425781, 173.22991943359375], "p3": [444.5282897949219, 292.6415100097656], "p4": [216.36839811310628, 297.4398272321224]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": false, "content": [{"bbox": null, "direction": null, "text": "2", "score": null}], "bbox": {"p1": [136.29154518950418, 176.43614886149186], "p2": [177.43622888053642, 176.43614886149186], "p3": [170.83486238532123, 295.9174311926606], "p4": [122.91743119266039, 296.4587155963304]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}