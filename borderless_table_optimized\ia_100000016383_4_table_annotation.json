{"table_ind": "ia_100000016383_4", "image_path": "ia_100000016383_4.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": true, "content": [{"bbox": null, "direction": null, "text": "Determine the resultant of each vector sum.\na) 34 km/h east and then 15 km/h north\nb) 100 m/s south and then 50 m/s west\nc) 45 km/h vertically and then 75 km/h\nhorizontally", "score": null}], "bbox": {"p1": [77.51542953886478, -2.1947267055511475], "p2": [490.0573461667455, 5.149253845214844], "p3": [488.14560976039814, 131.9913787841797], "p4": [76.00090089080209, 124.64739227294922]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "Suppose you are given the resultant and one vector in\nthe addition of two vectors. How would you find the\nother vector?", "score": null}], "bbox": {"p1": [76.00090089080209, 330.4486502189952], "p2": [488.14560976039814, 325.4302673339844], "p3": [488.14560976039814, 389.30405625269503], "p4": [77.51542953886478, 395.15806243704554]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": false, "content": [{"bbox": null, "direction": null, "text": "Three points, A, B, and C, are collinear, such that B is\nthe midpoint of AC. Let O be any non-collinear point.\nProve that OA + OC = 2OB.", "score": null}], "bbox": {"p1": [76.00090089080209, 241.08810330385964], "p2": [488.14560976039814, 241.08810330385964], "p3": [488.14560976039814, 330.4486502189952], "p4": [76.00090089080209, 330.4486502189952]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "Determine the horizontal and vertical vector\ncomponents of each force.\na) magnitude 1200 N, θ = 43<sup>o</sup>\ncounterclockwise from the horizontal\nb) magnitude 17 N, θ = 15<sup>o</sup> clockwise from\nthe vertical\nc) magnitude 400 N, θ = 12<sup>o</sup> clockwise\nfrom the vertical", "score": null}], "bbox": {"p1": [74.09784698486328, 395.15806243704554], "p2": [488.14560976039814, 389.30405625269503], "p3": [490.0573461667455, 579.1888427734375], "p4": [77.51542953886478, 585.8974731840361]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [{"bbox": null, "direction": null, "text": "Simplify each of the following algebraically.\na)3(u + v) - 3(u - v)\nb) 3u + 2v - 2(v-u) + (-3v)\nc) - (u + v) - 4(u - 2v)", "score": null}], "bbox": {"p1": [76.00090089080209, 122.68980868841338], "p2": [490.0573461667455, 128.65892357016492], "p3": [488.14560976039814, 248.0523681640625], "p4": [74.09784698486328, 241.08810330385964]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": false, "content": [{"bbox": null, "direction": null, "text": "Two non-parallel vectors have magnitudes of 5 km/h\nand 9 km/h. Can the sum of the vectors have a\nmagnitude of 14 km/h? Explain.", "score": null}], "bbox": {"p1": [76.00090089080209, 585.8974731840361], "p2": [488.14560976039814, 568.357666015625], "p3": [490.0573461667455, 630.9706686535721], "p4": [77.51542953886478, 647.1549910438252]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": true, "content": [{"bbox": null, "direction": null, "text": "3.", "score": null}], "bbox": {"p1": [13.0, 10.0], "p2": [77.51542953886478, 4.706341076561785], "p3": [76.00090089080209, 122.68980868841338], "p4": [6.173553719008396, 122.68980868841336]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": false, "content": [{"bbox": null, "direction": null, "text": "4.", "score": null}], "bbox": {"p1": [6.173553719008396, 122.68980868841336], "p2": [76.00090089080209, 122.68980868841338], "p3": [76.00090089080209, 241.08810330385964], "p4": [1.0, 241.08810330385964]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": false, "content": [{"bbox": null, "direction": null, "text": "7.", "score": null}], "bbox": {"p1": [0.0, 395.15806243704554], "p2": [77.51542953886478, 395.15806243704554], "p3": [77.51542953886478, 585.8974731840361], "p4": [3.0, 585.8974731840361]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 9, "header": false, "content": [{"bbox": null, "direction": null, "text": "5.", "score": null}], "bbox": {"p1": [2.0, 241.08810330385964], "p2": [76.00090089080209, 241.08810330385964], "p3": [76.00090089080209, 330.4486502189952], "p4": [0.0, 329.0]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 10, "header": false, "content": [{"bbox": null, "direction": null, "text": "6.", "score": null}], "bbox": {"p1": [0.0, 330.4486502189952], "p2": [76.00090089080209, 330.4486502189952], "p3": [77.51542953886478, 395.15806243704554], "p4": [0.0, 395.15806243704554]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 11, "header": false, "content": [{"bbox": null, "direction": null, "text": "8.", "score": null}], "bbox": {"p1": [3.0, 585.8974731840361], "p2": [77.51542953886478, 585.8974731840361], "p3": [77.51542953886478, 647.1549910438252], "p4": [3.8573975044564577, 650.3796791443847]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 12, "header": false, "content": [], "bbox": {"p1": [488.14560976039814, 330.4486502189952], "p2": [525.0, 330.4486502189952], "p3": [524.3738317757006, 389.30405625269503], "p4": [488.14560976039814, 389.30405625269503]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 13, "header": false, "content": [], "bbox": {"p1": [488.14560976039814, 389.30405625269503], "p2": [524.3738317757006, 389.30405625269503], "p3": [524.9999999999995, 570.5317324185248], "p4": [488.14560976039814, 570.5317324185248]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 14, "header": true, "content": [{"bbox": null, "direction": null, "text": "K", "score": null}], "bbox": {"p1": [490.0573461667455, 6.358490566037744], "p2": [524.9433962264152, 3.471698113207367], "p3": [525.0, 128.65892357016492], "p4": [488.14560976039814, 128.65892357016492]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 15, "header": false, "content": [{"bbox": null, "direction": null, "text": "K", "score": null}], "bbox": {"p1": [488.14560976039814, 128.65892357016492], "p2": [525.0, 128.65892357016492], "p3": [525.0, 244.24528301886744], "p4": [488.14560976039814, 244.24528301886744]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 16, "header": false, "content": [{"bbox": null, "direction": null, "text": "K", "score": null}], "bbox": {"p1": [488.14560976039814, 244.24528301886744], "p2": [525.0, 244.24528301886744], "p3": [525.0, 330.4486502189952], "p4": [488.14560976039814, 330.4486502189952]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 17, "header": false, "content": [], "bbox": {"p1": [488.14560976039814, 570.5317324185248], "p2": [524.9999999999995, 570.5317324185248], "p3": [524.7575757575755, 628.6363636363635], "p4": [490.0573461667455, 630.9706686535721]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}