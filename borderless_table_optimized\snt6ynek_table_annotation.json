{"table_ind": "snt6ynek", "image_path": "snt6ynek.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": false, "content": [{"bbox": null, "direction": null, "text": "288.Now that we know each other...", "score": null}], "bbox": {"p1": [0.0, 564.0256410256411], "p2": [300.**************, 544.8498583569403], "p3": [298.83666062184625, 573.4035087719299], "p4": [0.0, 593.7389033942559]}, "lloc": {"start_row": 18, "end_row": 18, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [], "bbox": {"p1": [300.**************, 544.8498583569403], "p2": [434.0, 536.0], "p3": [433.0, 562.0], "p4": [298.83666062184625, 573.4035087719299]}, "lloc": {"start_row": 18, "end_row": 18, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": false, "content": [{"bbox": null, "direction": null, "text": "290.get the hang of (doing) sth.", "score": null}], "bbox": {"p1": [0.0, 593.7389033942559], "p2": [298.83666062184625, 573.4035087719299], "p3": [298.83666062184625, 601.2500327854524], "p4": [0.0, 624.1931718990795]}, "lloc": {"start_row": 19, "end_row": 19, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "286.The plane crashed after taking off.", "score": null}], "bbox": {"p1": [0.0, 533.0178117048349], "p2": [300.**************, 516.2832861189804], "p3": [300.**************, 544.8498583569403], "p4": [0.0, 564.0256410256411]}, "lloc": {"start_row": 17, "end_row": 17, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [], "bbox": {"p1": [300.**************, 434.23529411764684], "p2": [435.************, 431.94117647058783], "p3": [435.86455331412117, 458.15273775216156], "p4": [300.**************, 461.2881844380404]}, "lloc": {"start_row": 14, "end_row": 14, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": false, "content": [], "bbox": {"p1": [298.83666062184625, 573.4035087719299], "p2": [434.0, 562.0], "p3": [432.0, 587.5], "p4": [298.83666062184625, 601.2500327854524]}, "lloc": {"start_row": 19, "end_row": 19, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": false, "content": [], "bbox": {"p1": [300.**************, 488.7118155619596], "p2": [434.49110320284717, 483.6441281138791], "p3": [434.0, 509.0], "p4": [300.**************, 516.2832861189804]}, "lloc": {"start_row": 16, "end_row": 16, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": false, "content": [{"bbox": null, "direction": null, "text": "280.give birth to", "score": null}], "bbox": {"p1": [0.0, 442.7389162561576], "p2": [300.**************, 434.23529411764684], "p3": [300.**************, 461.2881844380404], "p4": [0.0, 472.0]}, "lloc": {"start_row": 14, "end_row": 14, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": false, "content": [{"bbox": null, "direction": null, "text": "282.prevent/stop/keep...from...", "score": null}], "bbox": {"p1": [0.0, 472.0], "p2": [300.**************, 461.2881844380404], "p3": [300.**************, 488.7118155619596], "p4": [0.0, 502.0]}, "lloc": {"start_row": 15, "end_row": 15, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 9, "header": false, "content": [{"bbox": null, "direction": null, "text": "284.climate change weather", "score": null}], "bbox": {"p1": [0.0, 502.0], "p2": [300.**************, 488.7118155619596], "p3": [300.**************, 516.2832861189804], "p4": [0.0, 533.0178117048349]}, "lloc": {"start_row": 16, "end_row": 16, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 10, "header": false, "content": [], "bbox": {"p1": [300.**************, 516.2832861189804], "p2": [434.0, 509.0], "p3": [433.0, 536.0], "p4": [300.**************, 544.8498583569403]}, "lloc": {"start_row": 17, "end_row": 17, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 11, "header": true, "content": [{"bbox": null, "direction": null, "text": "252 in debt/out of debt ", "score": null}], "bbox": {"p1": [13.706484641637871, 62.331185099121285], "p2": [16.412969283276198, 33.63481228668934], "p3": [311.88936170212776, 63.08911843729584], "p4": [311.8159574468086, 89.9075109529687]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 12, "header": false, "content": [], "bbox": {"p1": [300.**************, 406.*************], "p2": [435.************, 406.*************], "p3": [435.************, 431.94117647058783], "p4": [300.**************, 434.23529411764684]}, "lloc": {"start_row": 13, "end_row": 13, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 13, "header": false, "content": [{"bbox": null, "direction": null, "text": "278.lay eggs(lay-laid-laid)", "score": null}], "bbox": {"p1": [0.0, 412.4842615012103], "p2": [300.**************, 406.*************], "p3": [300.**************, 434.23529411764684], "p4": [0.0, 442.7389162561576]}, "lloc": {"start_row": 13, "end_row": 13, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 14, "header": false, "content": [{"bbox": null, "direction": null, "text": "258.benefit from doing sth.", "score": null}], "bbox": {"p1": [10.952218430034463, 119.0], "p2": [307.74092574092583, 143.29870129870142], "p3": [307.74092574092583, 170.0], "p4": [10.0, 147.61111111111086]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 15, "header": false, "content": [], "bbox": {"p1": [298.83666062184625, 601.2500327854524], "p2": [433.0, 587.5], "p3": [433.0, 613.0], "p4": [298.83666062184625, 628.6462425443456]}, "lloc": {"start_row": 20, "end_row": 20, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 16, "header": true, "content": [], "bbox": {"p1": [311.88936170212776, 63.08911843729584], "p2": [444.2987012987014, 79.94805194805213], "p3": [443.67532467532465, 103.64935064935071], "p4": [311.8159574468086, 89.9075109529687]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 17, "header": false, "content": [], "bbox": {"p1": [302.**************, 353.96770334928226], "p2": [436.0, 353.96770334928226], "p3": [436.0, 381.*************], "p4": [300.**************, 381.*************]}, "lloc": {"start_row": 11, "end_row": 11, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 18, "header": false, "content": [], "bbox": {"p1": [302.**************, 327.2392344497607], "p2": [436.0430622009567, 330.67464114832546], "p3": [436.0, 353.96770334928226], "p4": [302.**************, 353.96770334928226]}, "lloc": {"start_row": 10, "end_row": 10, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 19, "header": false, "content": [{"bbox": null, "direction": null, "text": "262.be of benefit to sb.", "score": null}], "bbox": {"p1": [8.722222222222172, 176.16666666666652], "p2": [307.74092574092583, 197.0], "p3": [305.*************, 222.*************], "p4": [7.***************, 205.**************]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 20, "header": false, "content": [{"bbox": null, "direction": null, "text": "276.mammal/reptile/amphibian", "score": null}], "bbox": {"p1": [0.0, 381.*************], "p2": [300.**************, 381.*************], "p3": [300.**************, 406.*************], "p4": [0.0, 412.4842615012103]}, "lloc": {"start_row": 12, "end_row": 12, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 21, "header": false, "content": [], "bbox": {"p1": [311.8159574468086, 89.9075109529687], "p2": [443.67532467532465, 103.64935064935071], "p3": [443.2987012987014, 129.29870129870142], "p4": [310.3506493506493, 116.62337662337677]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 22, "header": false, "content": [], "bbox": {"p1": [300.**************, 461.2881844380404], "p2": [435.86455331412117, 458.15273775216156], "p3": [434.49110320284717, 483.6441281138791], "p4": [300.**************, 488.7118155619596]}, "lloc": {"start_row": 15, "end_row": 15, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 23, "header": false, "content": [], "bbox": {"p1": [302.**************, 300.4784688995219], "p2": [436.**************, 305.47846889952143], "p3": [436.0430622009567, 330.67464114832546], "p4": [302.**************, 327.2392344497607]}, "lloc": {"start_row": 9, "end_row": 9, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 24, "header": false, "content": [], "bbox": {"p1": [310.3506493506493, 116.62337662337677], "p2": [443.2987012987014, 129.29870129870142], "p3": [442.24675324675354, 154.62337662337632], "p4": [307.74092574092583, 143.29870129870142]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 25, "header": false, "content": [{"bbox": null, "direction": null, "text": "264.be beneficial to sb.", "score": null}], "bbox": {"p1": [7.***************, 205.**************], "p2": [305.*************, 222.*************], "p3": [305.*************, 248.**************], "p4": [5.***************, 233.**************]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 26, "header": false, "content": [{"bbox": null, "direction": null, "text": "266.Can you account for your..?", "score": null}], "bbox": {"p1": [5.***************, 233.**************], "p2": [305.*************, 248.**************], "p3": [302.**************, 274.*************], "p4": [3.****************, 262.**************]}, "lloc": {"start_row": 7, "end_row": 7, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 27, "header": false, "content": [], "bbox": {"p1": [300.**************, 381.*************], "p2": [435.0, 381.*************], "p3": [435.************, 406.*************], "p4": [300.**************, 406.*************]}, "lloc": {"start_row": 12, "end_row": 12, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 28, "header": false, "content": [], "bbox": {"p1": [302.**************, 274.*************], "p2": [437.*************, 280.*************], "p3": [436.**************, 305.47846889952143], "p4": [302.**************, 300.4784688995219]}, "lloc": {"start_row": 8, "end_row": 8, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 29, "header": false, "content": [{"bbox": null, "direction": null, "text": "260.benefit sb.", "score": null}], "bbox": {"p1": [10.0, 147.61111111111086], "p2": [307.74092574092583, 170.0], "p3": [307.74092574092583, 197.0], "p4": [8.722222222222172, 176.16666666666652]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 30, "header": false, "content": [], "bbox": {"p1": [307.74092574092583, 170.0], "p2": [441.67532467532465, 179.0], "p3": [439.6410256410254, 205.**************], "p4": [307.74092574092583, 197.0]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 31, "header": false, "content": [], "bbox": {"p1": [307.74092574092583, 143.29870129870142], "p2": [442.24675324675354, 154.62337662337632], "p3": [441.67532467532465, 179.0], "p4": [307.74092574092583, 170.0]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 32, "header": false, "content": [], "bbox": {"p1": [305.*************, 248.**************], "p2": [438.0384615384619, 254.64102564102586], "p3": [437.*************, 280.*************], "p4": [302.**************, 274.*************]}, "lloc": {"start_row": 7, "end_row": 7, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 33, "header": false, "content": [], "bbox": {"p1": [307.74092574092583, 197.0], "p2": [439.6410256410254, 205.**************], "p3": [438.64102564102586, 229.64102564102586], "p4": [305.*************, 222.*************]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 34, "header": false, "content": [{"bbox": null, "direction": null, "text": "274.Bacteria multiply in warm food.", "score": null}], "bbox": {"p1": [0.7177033492821465, 353.96770334928226], "p2": [302.**************, 353.96770334928226], "p3": [300.**************, 381.*************], "p4": [0.0, 381.*************]}, "lloc": {"start_row": 11, "end_row": 11, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 35, "header": false, "content": [{"bbox": null, "direction": null, "text": "268.seek one's advice()sought", "score": null}], "bbox": {"p1": [3.****************, 262.**************], "p2": [302.**************, 274.*************], "p3": [302.**************, 300.4784688995219], "p4": [2.771362586604937, 293.0]}, "lloc": {"start_row": 8, "end_row": 8, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 36, "header": false, "content": [], "bbox": {"p1": [305.*************, 222.*************], "p2": [438.64102564102586, 229.64102564102586], "p3": [438.0384615384619, 254.64102564102586], "p4": [305.*************, 248.**************]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 37, "header": false, "content": [{"bbox": null, "direction": null, "text": "270.hide and seek", "score": null}], "bbox": {"p1": [2.771362586604937, 293.0], "p2": [302.**************, 300.4784688995219], "p3": [302.**************, 327.2392344497607], "p4": [1.647058823529278, 322.23529411764684]}, "lloc": {"start_row": 9, "end_row": 9, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 38, "header": false, "content": [{"bbox": null, "direction": null, "text": "256.stare at/glare at one's enemy", "score": null}], "bbox": {"p1": [12.245733788395682, 89.9075109529687], "p2": [310.3506493506493, 116.62337662337677], "p3": [307.74092574092583, 143.29870129870142], "p4": [10.952218430034463, 119.0]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 39, "header": false, "content": [{"bbox": null, "direction": null, "text": "254. pay off all the debts", "score": null}], "bbox": {"p1": [13.706484641637871, 62.331185099121285], "p2": [311.8159574468086, 89.9075109529687], "p3": [310.3506493506493, 116.62337662337677], "p4": [12.245733788395682, 89.9075109529687]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 40, "header": false, "content": [{"bbox": null, "direction": null, "text": "272.oxygen/cardon dioxide", "score": null}], "bbox": {"p1": [1.647058823529278, 322.23529411764684], "p2": [302.**************, 327.2392344497607], "p3": [302.**************, 353.96770334928226], "p4": [0.7177033492821465, 353.96770334928226]}, "lloc": {"start_row": 10, "end_row": 10, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 41, "header": false, "content": [{"bbox": null, "direction": null, "text": "out with sb.", "score": null}], "bbox": {"p1": [-1.2085131406784058, 624.1931718990795], "p2": [298.83666062184625, 601.2500327854524], "p3": [300.**************, 630.438232421875], "p4": [1.029693603515625, 653.427734375]}, "lloc": {"start_row": 20, "end_row": 20, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}