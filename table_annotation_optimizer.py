#!/usr/bin/env python3
"""
表格标注优化器 - 使用改进的透视感知算法优化表格标注

主要功能:
1. 保持透视变换特性，支持四边形单元格
2. 自适应阈值计算，基于图像分辨率和表格尺寸
3. 保留原始quality和type属性不变
4. 支持批量处理和并行处理
5. 可配置输入输出路径和算法参数

基于透视感知算法，确保角点对齐的同时保持表格的自然透视特征。

作者: AI Assistant
版本: 3.0
更新日期: 2025-01-08
"""

import os
import json
import time
import math
import numpy as np
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
import argparse
from typing import List, Dict, Tuple, Optional, Any
from PIL import Image


# ==================== 配置区域 ====================
# 在这里修改处理参数
CONFIG = {
    # 输入输出路径配置
    'input_dir': 'borderless_table',           # 输入文件夹路径
    'output_dir': 'borderless_table_optimized', # 输出文件夹路径

    # 文件匹配模式
    'annotation_pattern': '*_table_annotation.json',  # 标注文件匹配模式
    'image_extensions': ['.jpg', '.jpeg', '.png', '.bmp', '.tiff'],  # 支持的图片格式

    # 算法参数
    'tolerance': 3.0,              # 基础容差阈值（像素）
    'preserve_perspective': True,   # 是否保持透视变换
    'adaptive_threshold': True,     # 是否使用自适应阈值

    # 处理参数
    'max_workers': 4,              # 并行处理线程数

    # 属性保护配置
    'preserve_attributes': ['quality', 'type'],  # 需要保护的属性列表
}
# ================================================


class PerspectiveAwareOptimizer:
    """透视感知表格标注优化器类"""

    def __init__(self, tolerance: float = 3.0, preserve_perspective: bool = True,
                 adaptive_threshold: bool = True):
        """
        初始化优化器

        Args:
            tolerance: 基础容差阈值（像素）
            preserve_perspective: 是否保持透视变换
            adaptive_threshold: 是否使用自适应阈值
        """
        self.base_tolerance = tolerance
        self.preserve_perspective = preserve_perspective
        self.adaptive_threshold = adaptive_threshold
        self.tolerance = tolerance
        self.image_info = None
        self.cells = []
        self.table_ind = ""
        self.image_path = ""
        self.grid_structure = {}

    def get_image_info(self, image_path: str) -> Optional[Dict[str, int]]:
        """
        获取图片信息

        Args:
            image_path: 图片文件路径

        Returns:
            包含图片宽度和高度的字典，失败时返回None
        """
        try:
            if image_path and Path(image_path).exists():
                with Image.open(image_path) as img:
                    return {'width': img.size[0], 'height': img.size[1]}
        except Exception as e:
            print(f"无法读取图片 {image_path}: {e}")
        return None

    def calculate_table_bounds(self, cells: List[Dict]) -> Tuple[float, float]:
        """
        计算表格边界尺寸

        Args:
            cells: 单元格列表

        Returns:
            表格宽度和高度的元组
        """
        if not cells:
            return 0.0, 0.0

        all_x = []
        all_y = []

        for cell in cells:
            bbox = cell['bbox']
            for point_name in ['p1', 'p2', 'p3', 'p4']:
                point = bbox[point_name]
                all_x.append(point[0])
                all_y.append(point[1])

        width = max(all_x) - min(all_x)
        height = max(all_y) - min(all_y)

        return width, height

    def calculate_adaptive_threshold(self, img_width: int, img_height: int,
                                   table_width: float, table_height: float) -> float:
        """
        基于图片分辨率和表格尺寸计算自适应阈值

        Args:
            img_width: 图片宽度
            img_height: 图片高度
            table_width: 表格宽度
            table_height: 表格高度

        Returns:
            计算得到的自适应阈值
        """
        if not self.adaptive_threshold:
            return self.base_tolerance

        # 1. 分辨率因子
        total_pixels = img_width * img_height
        resolution_factor = math.sqrt(total_pixels / (1920 * 1080))  # 以1080p为基准
        resolution_factor = max(0.5, min(2.0, resolution_factor))

        # 2. 表格尺寸因子
        if table_width > 0 and table_height > 0:
            table_area = table_width * table_height
            image_area = img_width * img_height
            area_ratio = table_area / image_area

            # 表格占比越大，需要更精确的阈值
            if area_ratio > 0.8:
                size_factor = 0.8
            elif area_ratio > 0.5:
                size_factor = 1.0
            else:
                size_factor = 1.2
        else:
            size_factor = 1.0

        # 3. 综合计算
        combined_factor = (0.6 * resolution_factor + 0.4 * size_factor)
        adaptive_threshold = self.base_tolerance * combined_factor

        # 限制阈值范围 [1.0, 10.0]
        adaptive_threshold = max(1.0, min(10.0, adaptive_threshold))

        return adaptive_threshold

    def load_annotation(self, json_path: str, image_path: Optional[str] = None):
        """
        加载表格标注文件

        Args:
            json_path: 标注文件路径
            image_path: 对应的图片文件路径（用于自适应阈值计算）
        """
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        self.cells = data['cells']
        self.table_ind = data.get('table_ind', '')
        self.image_path = data.get('image_path', '')

        # 获取图片信息用于自适应阈值计算
        if image_path:
            self.image_info = self.get_image_info(image_path)

            # 计算表格边界用于自适应阈值
            if self.image_info:
                table_width, table_height = self.calculate_table_bounds(self.cells)
                self.tolerance = self.calculate_adaptive_threshold(
                    self.image_info['width'],
                    self.image_info['height'],
                    table_width,
                    table_height
                )
                print(f"自适应阈值: {self.tolerance:.2f} (图片: {self.image_info['width']}x{self.image_info['height']}, 表格: {table_width:.1f}x{table_height:.1f})")
            else:
                print(f"无法获取图片信息，使用基础阈值: {self.base_tolerance}")
                self.tolerance = self.base_tolerance

        print(f"加载了 {len(self.cells)} 个单元格")

    def build_grid_structure(self):
        """构建表格的逻辑网格结构"""
        self.grid_structure = {}

        for cell in self.cells:
            lloc = cell['lloc']
            for r in range(lloc['start_row'], lloc['end_row'] + 1):
                for c in range(lloc['start_col'], lloc['end_col'] + 1):
                    if (r, c) not in self.grid_structure:
                        self.grid_structure[(r, c)] = []
                    self.grid_structure[(r, c)].append(cell)

    def get_shared_boundary_points(self, direction: str) -> List[Tuple[List[float], List[int]]]:
        """
        获取共享边界上的角点

        Args:
            direction: 'horizontal' 或 'vertical'

        Returns:
            (点坐标, 相关单元格索引列表) 的列表
        """
        boundary_points = []

        if direction == 'horizontal':
            # 处理水平边界（行之间的边界）
            rows = sorted(set(r for r, c in self.grid_structure.keys()))
            cols = sorted(set(c for r, c in self.grid_structure.keys()))

            for row_idx in range(len(rows) - 1):
                current_row = rows[row_idx]
                next_row = rows[row_idx + 1]

                for col in cols:
                    current_cells = self.grid_structure.get((current_row, col), [])
                    next_cells = self.grid_structure.get((next_row, col), [])

                    if current_cells and next_cells:
                        for curr_cell in current_cells:
                            for next_cell in next_cells:
                                if (curr_cell['lloc']['end_row'] == current_row and
                                    next_cell['lloc']['start_row'] == next_row):
                                    # 收集共享边界上的点
                                    curr_idx = self.cells.index(curr_cell)
                                    next_idx = self.cells.index(next_cell)

                                    # 当前单元格的下边界点
                                    boundary_points.append((curr_cell['bbox']['p3'], [curr_idx]))
                                    boundary_points.append((curr_cell['bbox']['p4'], [curr_idx]))

                                    # 下一个单元格的上边界点
                                    boundary_points.append((next_cell['bbox']['p1'], [next_idx]))
                                    boundary_points.append((next_cell['bbox']['p2'], [next_idx]))

        elif direction == 'vertical':
            # 处理垂直边界（列之间的边界）
            rows = sorted(set(r for r, c in self.grid_structure.keys()))
            cols = sorted(set(c for r, c in self.grid_structure.keys()))

            for col_idx in range(len(cols) - 1):
                current_col = cols[col_idx]
                next_col = cols[col_idx + 1]

                for row in rows:
                    current_cells = self.grid_structure.get((row, current_col), [])
                    next_cells = self.grid_structure.get((row, next_col), [])

                    if current_cells and next_cells:
                        for curr_cell in current_cells:
                            for next_cell in next_cells:
                                if (curr_cell['lloc']['end_col'] == current_col and
                                    next_cell['lloc']['start_col'] == next_col):
                                    # 收集共享边界上的点
                                    curr_idx = self.cells.index(curr_cell)
                                    next_idx = self.cells.index(next_cell)

                                    # 当前单元格的右边界点
                                    boundary_points.append((curr_cell['bbox']['p2'], [curr_idx]))
                                    boundary_points.append((curr_cell['bbox']['p3'], [curr_idx]))

                                    # 下一个单元格的左边界点
                                    boundary_points.append((next_cell['bbox']['p1'], [next_idx]))
                                    boundary_points.append((next_cell['bbox']['p4'], [next_idx]))

        return boundary_points

    def align_boundary_points(self, boundary_points: List[Tuple[List[float], List[int]]],
                            coordinate_index: int):
        """
        对齐边界上的角点

        Args:
            boundary_points: 边界点列表
            coordinate_index: 坐标索引（0=x, 1=y）
        """
        if not boundary_points:
            return

        # 按坐标值聚类相近的点
        clusters = []
        used_indices = set()

        for i, (point1, cells1) in enumerate(boundary_points):
            if i in used_indices:
                continue

            cluster_points = [(point1, cells1, i)]
            used_indices.add(i)

            for j, (point2, cells2) in enumerate(boundary_points):
                if j in used_indices:
                    continue

                # 检查是否在容差范围内
                if abs(point1[coordinate_index] - point2[coordinate_index]) <= self.tolerance:
                    cluster_points.append((point2, cells2, j))
                    used_indices.add(j)

            if len(cluster_points) > 1:
                clusters.append(cluster_points)

        # 对每个聚类进行对齐
        for cluster in clusters:
            # 计算平均坐标
            avg_coord = sum(point[coordinate_index] for point, _, _ in cluster) / len(cluster)

            # 更新所有相关单元格的角点
            for point, cell_indices, _ in cluster:
                for cell_idx in cell_indices:
                    cell = self.cells[cell_idx]
                    bbox = cell['bbox']

                    # 找到对应的角点并更新
                    for point_name in ['p1', 'p2', 'p3', 'p4']:
                        if (abs(bbox[point_name][0] - point[0]) < 0.1 and
                            abs(bbox[point_name][1] - point[1]) < 0.1):
                            bbox[point_name][coordinate_index] = avg_coord

    def align_row_boundaries(self):
        """对齐行边界（水平对齐）"""
        boundary_points = self.get_shared_boundary_points('horizontal')
        self.align_boundary_points(boundary_points, 1)  # 对齐Y坐标

    def align_column_boundaries(self):
        """对齐列边界（垂直对齐）"""
        boundary_points = self.get_shared_boundary_points('vertical')
        self.align_boundary_points(boundary_points, 0)  # 对齐X坐标

    def fine_tune_nearby_points(self):
        """微调相近的角点"""
        all_points = []

        # 收集所有角点
        for cell_idx, cell in enumerate(self.cells):
            bbox = cell['bbox']
            for point_name in ['p1', 'p2', 'p3', 'p4']:
                point = bbox[point_name]
                all_points.append({
                    'coords': point,
                    'cell_idx': cell_idx,
                    'point_name': point_name
                })

        # 使用较小的阈值进行微调
        micro_threshold = self.tolerance * 0.5
        aligned_count = 0

        # 简单的聚类和对齐
        used_indices = set()

        for i, point1 in enumerate(all_points):
            if i in used_indices:
                continue

            cluster = [point1]
            used_indices.add(i)

            for j, point2 in enumerate(all_points):
                if j in used_indices:
                    continue

                # 计算距离
                dist = math.sqrt(
                    (point1['coords'][0] - point2['coords'][0])**2 +
                    (point1['coords'][1] - point2['coords'][1])**2
                )

                if dist <= micro_threshold:
                    cluster.append(point2)
                    used_indices.add(j)

            # 如果聚类包含多个点，进行对齐
            if len(cluster) > 1:
                # 计算重心
                center_x = sum(p['coords'][0] for p in cluster) / len(cluster)
                center_y = sum(p['coords'][1] for p in cluster) / len(cluster)

                # 更新所有点到重心位置
                for point in cluster:
                    cell = self.cells[point['cell_idx']]
                    cell['bbox'][point['point_name']][0] = center_x
                    cell['bbox'][point['point_name']][1] = center_y

                aligned_count += len(cluster)

        print(f"微调了 {aligned_count} 个相近角点")


    def optimize_alignment(self):
        """执行透视感知的角点对齐优化"""
        print("构建网格结构...")
        self.build_grid_structure()

        print("对齐行边界...")
        self.align_row_boundaries()

        print("对齐列边界...")
        self.align_column_boundaries()

        print("微调相近角点...")
        self.fine_tune_nearby_points()

        print("透视感知优化完成！")

    def save_optimized_annotation(self, output_path: str):
        """
        保存优化后的标注文件，保持原有属性不变

        Args:
            output_path: 输出文件路径
        """
        output_data = {
            'table_ind': self.table_ind,
            'image_path': self.image_path,
            'type': 1,  # 保持type属性
            'cells': self.cells
        }

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)

        print(f"优化后的标注文件已保存到: {output_path}")

    def optimize_table_annotation(self, annotation_file: str, output_file: Optional[str] = None,
                                image_file: Optional[str] = None) -> Dict[str, Any]:
        """
        优化表格标注文件的主入口方法

        Args:
            annotation_file: 输入的标注文件路径
            output_file: 输出文件路径，如果为None则覆盖原文件
            image_file: 对应的图片文件路径，用于自适应阈值计算

        Returns:
            包含优化结果的字典
        """
        try:
            # 加载标注文件
            self.load_annotation(annotation_file, image_file)

            # 执行优化
            self.optimize_alignment()

            # 保存结果
            if output_file is None:
                output_file = annotation_file

            self.save_optimized_annotation(output_file)

            return {
                'success': True,
                'input_file': annotation_file,
                'output_file': output_file,
                'cell_count': len(self.cells),
                'adaptive_threshold': self.tolerance if self.adaptive_threshold else None,
                'image_info': self.image_info
            }

        except Exception as e:
            return {
                'success': False,
                'input_file': annotation_file,
                'output_file': output_file or annotation_file,
                'error': str(e)
            }


class BatchProcessor:
    """批量处理器类"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化批量处理器

        Args:
            config: 配置字典
        """
        self.config = config

    def find_annotation_files(self, input_dir: str) -> List[Path]:
        """查找所有标注文件"""
        input_path = Path(input_dir)
        pattern = self.config.get('annotation_pattern', '*_table_annotation.json')
        return list(input_path.glob(pattern))

    def find_corresponding_image(self, annotation_file: Path) -> Optional[str]:
        """查找对应的图片文件"""
        base_name = annotation_file.stem.replace('_table_annotation', '')
        image_dir = annotation_file.parent

        image_extensions = self.config.get('image_extensions', ['.jpg', '.jpeg', '.png', '.bmp'])

        for ext in image_extensions:
            image_path = image_dir / f"{base_name}{ext}"
            if image_path.exists():
                return str(image_path)

        return None

    def process_batch(self) -> Dict[str, Any]:
        """执行批量处理"""
        input_dir = self.config['input_dir']
        output_dir = self.config['output_dir']
        max_workers = self.config.get('max_workers', 4)

        # 查找所有标注文件
        annotation_files = self.find_annotation_files(input_dir)

        if not annotation_files:
            return {
                'success': False,
                'error': f'在 {input_dir} 中未找到标注文件'
            }

        print(f"找到 {len(annotation_files)} 个标注文件")
        print(f"输出目录: {output_dir}")
        print(f"并行处理数: {max_workers}")
        print("=" * 60)

        # 统计信息
        stats = {
            'total_files': len(annotation_files),
            'successful': 0,
            'failed': 0,
            'total_time': 0,
            'results': []
        }

        start_time = time.time()

        # 并行处理
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交任务
            future_to_file = {}
            for annotation_file in annotation_files:
                # 确定输出文件路径
                relative_path = annotation_file.relative_to(input_dir)
                output_file = Path(output_dir) / relative_path

                # 查找对应图片
                image_file = self.find_corresponding_image(annotation_file)

                # 创建优化器实例
                optimizer = PerspectiveAwareOptimizer(
                    tolerance=self.config.get('tolerance', 3.0),
                    preserve_perspective=self.config.get('preserve_perspective', True),
                    adaptive_threshold=self.config.get('adaptive_threshold', True)
                )

                future = executor.submit(
                    optimizer.optimize_table_annotation,
                    str(annotation_file),
                    str(output_file),
                    image_file
                )
                future_to_file[future] = annotation_file

            # 处理结果
            for i, future in enumerate(as_completed(future_to_file), 1):
                annotation_file = future_to_file[future]
                result = future.result()

                stats['results'].append(result)

                if result['success']:
                    stats['successful'] += 1
                    print(f"[{i:3d}/{len(annotation_files)}] ✅ {annotation_file.name}")
                    if 'adaptive_threshold' in result and result['adaptive_threshold']:
                        print(f"    自适应阈值: {result['adaptive_threshold']:.2f}")
                else:
                    stats['failed'] += 1
                    print(f"[{i:3d}/{len(annotation_files)}] ❌ {annotation_file.name} - {result['error']}")

        stats['total_time'] = time.time() - start_time

        # 输出统计
        print("\n" + "=" * 60)
        print("批量处理完成")
        print("=" * 60)
        print(f"总文件数: {stats['total_files']}")
        print(f"成功处理: {stats['successful']}")
        print(f"处理失败: {stats['failed']}")
        print(f"成功率: {stats['successful']/stats['total_files']*100:.1f}%")
        print(f"总处理时间: {stats['total_time']:.1f}秒")

        return stats


# ==================== 默认配置区域 ====================
# 这里是默认配置，可以通过命令行参数覆盖
DEFAULT_CONFIG = {
    # 输入输出路径配置
    'input_dir': 'borderless_table',           # 输入文件夹路径
    'output_dir': 'borderless_table_optimized', # 输出文件夹路径

    # 文件匹配模式
    'annotation_pattern': '*_table_annotation.json',  # 标注文件匹配模式
    'image_extensions': ['.jpg', '.jpeg', '.png', '.bmp', '.tiff'],  # 支持的图片格式

    # 算法参数
    'tolerance': 3.0,              # 基础容差阈值（像素）
    'preserve_perspective': True,   # 是否保持透视变换
    'adaptive_threshold': True,     # 是否使用自适应阈值

    # 处理参数
    'max_workers': 4,              # 并行处理线程数
}
# ================================================


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='表格标注优化器 - 使用透视感知算法优化表格角点对齐',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python table_annotation_optimizer.py
  python table_annotation_optimizer.py --input-dir my_data --output-dir my_output
  python table_annotation_optimizer.py --tolerance 5.0 --workers 8
  python table_annotation_optimizer.py --no-adaptive --no-perspective
        """
    )

    parser.add_argument('--input-dir', help='输入目录路径（默认: borderless_table）')
    parser.add_argument('--output-dir', help='输出目录路径（默认: borderless_table_optimized）')
    parser.add_argument('--tolerance', type=float, help='基础容差阈值，单位像素（默认: 3.0）')
    parser.add_argument('--workers', type=int, help='并行处理线程数（默认: 4）')
    parser.add_argument('--no-perspective', action='store_true', help='不保持透视变换，强制矩形对齐')
    parser.add_argument('--no-adaptive', action='store_true', help='不使用自适应阈值，使用固定阈值')
    parser.add_argument('--pattern', help='标注文件匹配模式（默认: *_table_annotation.json）')

    args = parser.parse_args()

    # 更新配置
    config = DEFAULT_CONFIG.copy()
    if args.input_dir:
        config['input_dir'] = args.input_dir
    if args.output_dir:
        config['output_dir'] = args.output_dir
    if args.tolerance:
        config['tolerance'] = args.tolerance
    if args.workers:
        config['max_workers'] = args.workers
    if args.no_perspective:
        config['preserve_perspective'] = False
    if args.no_adaptive:
        config['adaptive_threshold'] = False
    if args.pattern:
        config['annotation_pattern'] = args.pattern

    # 检查输入目录
    if not os.path.exists(config['input_dir']):
        print(f"❌ 错误: 输入目录 {config['input_dir']} 不存在")
        return 1

    print("=" * 60)
    print("🔧 表格标注优化器 v3.0")
    print("=" * 60)
    print(f"📁 输入目录: {config['input_dir']}")
    print(f"📁 输出目录: {config['output_dir']}")
    print(f"🎯 基础阈值: {config['tolerance']} 像素")
    print(f"🔄 保持透视: {'是' if config['preserve_perspective'] else '否'}")
    print(f"📊 自适应阈值: {'是' if config['adaptive_threshold'] else '否'}")
    print(f"⚡ 并行线程: {config['max_workers']}")
    print(f"🔍 文件模式: {config['annotation_pattern']}")
    print()

    # 执行批量处理
    processor = BatchProcessor(config)
    stats = processor.process_batch()

    if stats.get('success', True) and stats['failed'] == 0:
        print(f"\n✅ 处理完成！优化后的文件保存在: {config['output_dir']}")
        print(f"📊 处理统计: {stats['successful']}/{stats['total_files']} 成功")
        return 0
    else:
        print(f"\n❌ 处理过程中出现错误")
        if 'error' in stats:
            print(f"错误信息: {stats['error']}")
        return 1


if __name__ == "__main__":
    exit(main())
