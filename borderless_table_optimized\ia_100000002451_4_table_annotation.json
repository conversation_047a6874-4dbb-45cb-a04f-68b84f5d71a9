{"table_ind": "ia_100000002451_4", "image_path": "ia_100000002451_4.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": false, "content": [{"bbox": null, "direction": null, "text": "Recognizing individual sounds\nDiscriminating between sounds\nldentifying reduced sounds in connected speech (elision,\nassimilation, weak forms, such as 'schwa')\nIdentifying stressed syllables\nIdentifying changes in intonation", "score": null}], "bbox": {"p1": [259.06678646772343, 53.31205889709994], "p2": [944.7116088867188, 53.31205889709994], "p3": [945.1304931640625, 244.7371734879974], "p4": [259.06678646772343, 246.78683471679688]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "Perception", "score": null}], "bbox": {"p1": [21.899159663865248, 46.42016806722722], "p2": [259.06678646772343, 53.31205889709994], "p3": [259.06678646772343, 244.7371734879974], "p4": [19.156253814697266, 237.00001525878906]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": false, "content": [{"bbox": null, "direction": null, "text": "Matching sounds\nto language\nitems in an effort\nto move towards\nunderstanding\nmeaning", "score": null}], "bbox": {"p1": [19.156253814697266, 237.00001525878906], "p2": [265.6165771484375, 242.18618774414062], "p3": [259.06678646772343, 575.731201171875], "p4": [12.013252258300781, 570.3377075195312]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "ldentifying individual word boundaries\nldentifying words\nActivating grammatical and semantic information about the\nwords to build an idea about how they might be connected in\nsyntactic units\nldentifying 'key' words which give an idea about the topic\n(proposition) of the message\nldentifying discourse markers which organize and show atitudes\ntowards what is being said\nInnferring the meaning of unknown words", "score": null}], "bbox": {"p1": [257.2173767089844, 244.7371734879974], "p2": [945.1222534179688, 242.18618774414062], "p3": [946.3040771484375, 569.5178833007812], "p4": [259.06678646772343, 572.0]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": true, "content": [{"bbox": null, "direction": null, "text": "Examples", "score": null}], "bbox": {"p1": [259.06678646772343, 17.025669366885495], "p2": [937.0000610351562, 16.999963760375977], "p3": [937.0014038085938, 53.31205889709994], "p4": [259.06678646772343, 53.31205889709994]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": true, "content": [{"bbox": null, "direction": null, "text": "Skill", "score": null}], "bbox": {"p1": [22.0, 10.0], "p2": [259.06678646772343, 17.025669366885495], "p3": [259.06678646772343, 53.31205889709994], "p4": [21.899159663865248, 46.42016806722722]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}