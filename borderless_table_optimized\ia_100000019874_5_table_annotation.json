{"table_ind": "ia_100000019874_5", "image_path": "ia_100000019874_5.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": true, "content": [{"bbox": null, "direction": null, "text": "For the year Jan. 1-Dec. 31, 2011, or other tax year begining    ,2011, ending", "score": null}], "bbox": {"p1": [1021.9999389648438, 73.99995422363281], "p2": [1029.5560302734375, 110.62835693359375], "p3": [35.777984619140625, 316.04129464285717], "p4": [28.24989891052246, 279.00002034505206]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "Your first name and inital", "score": null}], "bbox": {"p1": [540.891330691511, 211.4346947681779], "p2": [556.25, 279.00002034505206], "p3": [49.0, 382.5], "p4": [35.777984619140625, 316.04129464285717]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": false, "content": [{"bbox": null, "direction": null, "text": "If a joint return, spouse's frist name and initial", "score": null}], "bbox": {"p1": [556.25, 279.00002034505206], "p2": [570.7878787878799, 345.1363636363635], "p3": [62.99992561340332, 450.0], "p4": [49.0, 382.5]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "Last name", "score": null}], "bbox": {"p1": [1023.125, 109.25], "p2": [1023.0625, 175.25], "p3": [556.25, 279.00002034505206], "p4": [540.891330691511, 211.4346947681779]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [{"bbox": null, "direction": null, "text": "Last name", "score": null}], "bbox": {"p1": [1023.0625, 175.25], "p2": [1024.0, 246.00005086263022], "p3": [570.7878787878799, 345.1363636363635], "p4": [556.25, 279.00002034505206]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": false, "content": [{"bbox": null, "direction": null, "text": "Home address (number and street). If you have a P.O.box, see instruction.", "score": null}], "bbox": {"p1": [1024.0, 246.00005086263022], "p2": [1038.8363037109375, 316.04129464285717], "p3": [77.56573104858398, 520.2904261309352], "p4": [62.99992561340332, 450.0]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 0, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": false, "content": [{"bbox": null, "direction": null, "text": "City, town or post office, state, and ZIP code. If you have a foreign address, also complete spaces below(see instruct", "score": null}], "bbox": {"p1": [1023.9999389648438, 316.04129464285717], "p2": [1039.0386962890625, 387.24980511581697], "p3": [92.33404541015625, 590.1395263671875], "p4": [77.56573104858398, 520.2904261309352]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 0, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": false, "content": [{"bbox": null, "direction": null, "text": "Foreign country name", "score": null}], "bbox": {"p1": [721.375, 455.25], "p2": [737.3516949152545, 520.2904261309352], "p3": [105.30862856714884, 658.1014343494871], "p4": [91.5, 586.25]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": false, "content": [{"bbox": null, "direction": null, "text": "Foreign province/county", "score": null}], "bbox": {"p1": [721.375, 455.25], "p2": [1024.0, 387.24980511581697], "p3": [1025.0, 459.0], "p4": [737.3516949152545, 521.6536687406137]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}