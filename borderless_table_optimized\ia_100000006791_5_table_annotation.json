{"table_ind": "ia_100000006791_5", "image_path": "ia_100000006791_5.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": true, "content": [{"bbox": null, "direction": null, "text": "Saving Fund Account", "score": null}], "bbox": {"p1": [44.0, 376.0], "p2": [363.**************, 373.*************], "p3": [363.**************, 426.************], "p4": [43.**************, 429.0]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": true, "content": [{"bbox": null, "direction": null, "text": "<PERSON><PERSON><PERSON><PERSON> /Sal<PERSON>/Total Freedom Salary/<PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON>/<PERSON><PERSON>\n(Women)/Others .PI. Specify ....", "score": null}], "bbox": {"p1": [363.**************, 373.*************], "p2": [1024.0, 365.*************], "p3": [1024.*************, 418.*************], "p4": [363.**************, 426.************]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": false, "content": [{"bbox": null, "direction": null, "text": "Recurring De<PERSON><PERSON>t", "score": null}], "bbox": {"p1": [41.**************, 491.*************], "p2": [363.**************, 487.*************], "p3": [363.**************, 523.*************], "p4": [41.**************, 527.*************]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "Fles-<PERSON><PERSON><PERSON><PERSON>", "score": null}], "bbox": {"p1": [41.**************, 527.*************], "p2": [363.**************, 523.*************], "p3": [363.**************, 560.*************], "p4": [39.**************, 565.*************]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [{"bbox": null, "direction": null, "text": "PNB Smart Banking CA", "score": null}], "bbox": {"p1": [42.***************, 456.*************], "p2": [363.**************, 456.*************], "p3": [363.**************, 487.*************], "p4": [41.**************, 491.*************]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": false, "content": [{"bbox": null, "direction": null, "text": "Mthly Core Amount Rs No of Instalments ......", "score": null}], "bbox": {"p1": [363.**************, 523.*************], "p2": [1023.*************, 519.*************], "p3": [1023.0, 557.0], "p4": [363.**************, 560.*************]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": false, "content": [{"bbox": null, "direction": null, "text": "Mthly Instalment Rs. No of Installments ......", "score": null}], "bbox": {"p1": [363.**************, 487.*************], "p2": [1024.3442993164062, 480.8658281409223], "p3": [1023.*************, 519.*************], "p4": [363.**************, 523.*************]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": false, "content": [{"bbox": null, "direction": null, "text": "PNB Prudent Sweep SF", "score": null}], "bbox": {"p1": [43.**************, 429.0], "p2": [363.**************, 426.************], "p3": [363.**************, 456.*************], "p4": [42.***************, 456.*************]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": false, "content": [{"bbox": null, "direction": null, "text": "Sweep In and Out Facility Required for _", "score": null}], "bbox": {"p1": [363.**************, 426.************], "p2": [1024.*************, 418.*************], "p3": [1024.3442993164062, 480.8658281409223], "p4": [363.**************, 487.*************]}, "lloc": {"start_row": 1, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}