{"table_ind": "ia_100000015960_4", "image_path": "ia_100000015960_4.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": false, "content": [{"bbox": null, "direction": null, "text": "How long did it take the ball to travel\nhalf the distance found in part A?\n(include units with answer)", "score": null}], "bbox": {"p1": [72.89188071960916, 393.0217731592449], "p2": [324.7870686848958, 393.02177315924484], "p3": [324.7870686848958, 495.00006103515625], "p4": [72.89188071960916, 496.5628662109375]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "# tries 0 show Details", "score": null}], "bbox": {"p1": [502.67838062881356, 265.3002899169922], "p2": [655.7905883789062, 265.3002899169922], "p3": [655.8102416992188, 391.24690410685815], "p4": [502.67838062881356, 391.24690410685815]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": false, "content": [{"bbox": null, "direction": null, "text": "# tries 0 show Details", "score": null}], "bbox": {"p1": [502.67838062881356, 130.41998291015625], "p2": [662.1857299804688, 130.395263671875], "p3": [662.2067260742188, 265.3002899169922], "p4": [502.67838062881356, 265.3002899169922]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "How fast was the ball going when it\nhad traveled half the distance found\nin part A?\n(include units with answer)", "score": null}], "bbox": {"p1": [65.7627141779119, 265.3002899169922], "p2": [322.7481230319145, 265.3002899169922], "p3": [322.7481230319145, 393.0217731592449], "p4": [65.7627141779119, 393.02177315924484]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [{"bbox": null, "direction": null, "text": "How long does it take to stop?\n(include units with answer)", "score": null}], "bbox": {"p1": [60.098500560573484, 132.14898814088997], "p2": [320.4290466308594, 132.14898814088997], "p3": [320.4290466308594, 265.3002899169922], "p4": [60.098500560573484, 265.3002899169922]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": true, "content": [{"bbox": null, "direction": null, "text": "A", "score": null}], "bbox": {"p1": [51.43398607215772, 1.1585966468812785], "p2": [317.6295645577567, -0.01796722412109375], "p3": [317.6295645577567, 132.14898814088997], "p4": [52.0269775390625, 132.14898814088997]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": false, "content": [{"bbox": null, "direction": null, "text": "# tries 0 show Details", "score": null}], "bbox": {"p1": [500.9666748046875, 391.24690410685815], "p2": [651.8403930664062, 391.24690410685815], "p3": [651.9404907226562, 496.3681945800781], "p4": [500.9666748046875, 496.5102137351521]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": true, "content": [{"bbox": null, "direction": null, "text": "# tries 0 show Details", "score": null}], "bbox": {"p1": [506.07322078272074, 0.3484320557490719], "p2": [666.0, 0.0], "p3": [662.185770750988, 132.14898814088997], "p4": [506.07322078272074, 132.14898814088997]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": false, "content": [{"bbox": null, "direction": null, "text": "17.88 pts. 110%\n2% try penalty\nHints: 1,0", "score": null}], "bbox": {"p1": [413.0047132156004, 132.14898814088997], "p2": [506.07322078272074, 132.14898814088997], "p3": [502.67838062881356, 265.3002899169922], "p4": [413.0047132156004, 265.3002899169922]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 9, "header": false, "content": [{"bbox": null, "direction": null, "text": "Format Check", "score": null}], "bbox": {"p1": [320.4290466308594, 265.3002899169922], "p2": [413.0047132156004, 265.3002899169922], "p3": [413.0047132156004, 391.24690410685815], "p4": [322.7481230319145, 393.0217731592449]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 10, "header": true, "content": [{"bbox": null, "direction": null, "text": "Format Check", "score": null}], "bbox": {"p1": [317.6295645577567, -0.01796722412109375], "p2": [413.0047132156004, 0.0], "p3": [413.0047132156004, 132.14898814088997], "p4": [317.6295645577567, 132.14898814088997]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 11, "header": false, "content": [{"bbox": null, "direction": null, "text": "Format Check", "score": null}], "bbox": {"p1": [322.7481230319145, 393.0217731592449], "p2": [413.0047132156004, 391.24690410685815], "p3": [413.0047132156004, 496.1700680272106], "p4": [324.7870686848958, 496.0]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 12, "header": true, "content": [{"bbox": null, "direction": null, "text": "17.88 pts. 110%\n2% try penalty\nHints: 1,0", "score": null}], "bbox": {"p1": [413.0047132156004, 0.0], "p2": [506.07322078272074, 0.3484320557490719], "p3": [506.07322078272074, 132.14898814088997], "p4": [413.0047132156004, 132.14898814088997]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 13, "header": false, "content": [{"bbox": null, "direction": null, "text": "17.88 pts. 110%\n2% try penalty\nHints: 2,0", "score": null}], "bbox": {"p1": [413.0047132156004, 265.3002899169922], "p2": [502.67838062881356, 265.3002899169922], "p3": [502.67838062881356, 391.24690410685815], "p4": [413.0047132156004, 391.24690410685815]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 14, "header": false, "content": [{"bbox": null, "direction": null, "text": "17.88 pts. 110%\n2% try penalty", "score": null}], "bbox": {"p1": [413.0047132156004, 391.24690410685815], "p2": [502.67838062881356, 391.24690410685815], "p3": [500.9666748046875, 496.5102137351521], "p4": [413.0047132156004, 496.1700680272106]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 15, "header": false, "content": [{"bbox": null, "direction": null, "text": "Format Check", "score": null}], "bbox": {"p1": [317.6295645577567, 132.14898814088997], "p2": [413.0047132156004, 132.14898814088997], "p3": [413.0047132156004, 265.3002899169922], "p4": [320.4290466308594, 265.3002899169922]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 16, "header": false, "content": [{"bbox": null, "direction": null, "text": "D", "score": null}], "bbox": {"p1": [15.27027027027043, 393.02177315924484], "p2": [72.89188071960916, 393.0217731592449], "p3": [77.63235294117658, 495.26470588235316], "p4": [22.0, 496.0]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 17, "header": false, "content": [{"bbox": null, "direction": null, "text": "C", "score": null}], "bbox": {"p1": [9.381818181817835, 265.3002899169922], "p2": [65.7627141779119, 265.3002899169922], "p3": [72.89188071960916, 393.0217731592449], "p4": [15.27027027027043, 393.02177315924484]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 18, "header": false, "content": [{"bbox": null, "direction": null, "text": "B", "score": null}], "bbox": {"p1": [0.3664055343642758, 134.48417483559615], "p2": [60.098500560573484, 132.14898814088997], "p3": [65.7627141779119, 265.3002899169922], "p4": [9.381818181817835, 265.3002899169922]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 19, "header": true, "content": [{"bbox": null, "direction": null, "text": "A", "score": null}], "bbox": {"p1": [0.0, 0.0], "p2": [51.43398607215772, 1.1585966468812785], "p3": [60.098500560573484, 132.14898814088997], "p4": [0.3664055343642758, 134.48417483559615]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}