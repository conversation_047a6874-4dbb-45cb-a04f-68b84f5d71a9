{"table_ind": "3137925586", "image_path": "3137925586.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": false, "content": [{"bbox": null, "direction": null, "text": "F22", "score": null}], "bbox": {"p1": [48.0, 608.8881158730637], "p2": [109.55018667567174, 608.8881158730637], "p3": [106.84662576687106, 636.9739196777343], "p4": [45.77675840978554, 636.9739196777343]}, "lloc": {"start_row": 22, "end_row": 22, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "5", "score": null}], "bbox": {"p1": [106.84662576687106, 172.57704254137906], "p2": [172.97030749266386, 172.57704254137903], "p3": [172.97030749266386, 197.78389482909725], "p4": [109.55018667567174, 197.78389482909725]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": false, "content": [{"bbox": null, "direction": null, "text": "10", "score": null}], "bbox": {"p1": [106.84662576687106, 636.9739196777343], "p2": [172.97030749266386, 636.9739196777343], "p3": [168.54534677444354, 681.8348811438323], "p4": [103.13633265166997, 683.5]}, "lloc": {"start_row": 23, "end_row": 23, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "F19", "score": null}], "bbox": {"p1": [57.0, 528.6019367991845], "p2": [118.44325153374257, 528.6019367991845], "p3": [115.2526867627781, 554.4573646102112], "p4": [54.38837920489277, 554.4573646102112]}, "lloc": {"start_row": 19, "end_row": 19, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [{"bbox": null, "direction": null, "text": "F20", "score": null}], "bbox": {"p1": [54.38837920489277, 554.4573646102112], "p2": [115.2526867627781, 554.4573646102112], "p3": [115.2526867627781, 581.5711899503655], "p4": [50.61162079510723, 581.5711899503655]}, "lloc": {"start_row": 20, "end_row": 20, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": false, "content": [{"bbox": null, "direction": null, "text": "7.5", "score": null}], "bbox": {"p1": [115.2526867627781, 554.4573646102112], "p2": [179.62735822368495, 554.4573646102112], "p3": [176.32484902887003, 581.5711899503655], "p4": [112.06066802999278, 581.5711899503655]}, "lloc": {"start_row": 20, "end_row": 20, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": false, "content": [{"bbox": null, "direction": null, "text": "5", "score": null}], "bbox": {"p1": [109.55018667567174, 608.8881158730637], "p2": [176.32484902887003, 608.8881158730637], "p3": [172.97030749266386, 636.9739196777343], "p4": [106.84662576687106, 636.9739196777343]}, "lloc": {"start_row": 22, "end_row": 22, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": false, "content": [{"bbox": null, "direction": null, "text": "7.5", "score": null}], "bbox": {"p1": [118.44325153374257, 528.6019367991845], "p2": [182.22501316400826, 526.1160215791942], "p3": [179.62735822368495, 554.4573646102112], "p4": [115.2526867627781, 554.4573646102112]}, "lloc": {"start_row": 19, "end_row": 19, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": false, "content": [{"bbox": null, "direction": null, "text": "F23", "score": null}], "bbox": {"p1": [45.77675840978554, 636.9739196777343], "p2": [106.84662576687106, 636.9739196777343], "p3": [103.13633265166997, 683.5], "p4": [39.0, 685.0]}, "lloc": {"start_row": 23, "end_row": 23, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 9, "header": false, "content": [{"bbox": null, "direction": null, "text": "20", "score": null}], "bbox": {"p1": [103.13633265166997, 144.68952041648262], "p2": [168.54534677444354, 144.68952041648262], "p3": [172.97030749266386, 172.57704254137903], "p4": [106.84662576687106, 172.57704254137906]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 10, "header": false, "content": [{"bbox": null, "direction": null, "text": "5", "score": null}], "bbox": {"p1": [112.06066802999278, 581.5711899503655], "p2": [176.32484902887003, 581.5711899503655], "p3": [176.32484902887003, 608.8881158730637], "p4": [109.55018667567174, 608.8881158730637]}, "lloc": {"start_row": 21, "end_row": 21, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 11, "header": false, "content": [{"bbox": null, "direction": null, "text": "F21", "score": null}], "bbox": {"p1": [50.61162079510723, 581.5711899503655], "p2": [115.2526867627781, 581.5711899503655], "p3": [109.55018667567174, 608.8881158730637], "p4": [48.0, 608.8881158730637]}, "lloc": {"start_row": 21, "end_row": 21, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 12, "header": false, "content": [{"bbox": null, "direction": null, "text": "5", "score": null}], "bbox": {"p1": [122.52508960573475, 503.2293577981651], "p2": [184.84943918933936, 503.2293577981651], "p3": [182.22501316400826, 526.1160215791942], "p4": [118.44325153374257, 528.6019367991845]}, "lloc": {"start_row": 18, "end_row": 18, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 13, "header": true, "content": [{"bbox": null, "direction": null, "text": "Amps", "score": null}], "bbox": {"p1": [100.8408997955008, 65.40899795500991], "p2": [165.638391345796, 65.59523809523807], "p3": [165.638391345796, 93.81863503885762], "p4": [100.8408997955008, 93.81863503885762]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 14, "header": false, "content": [{"bbox": null, "direction": null, "text": "20", "score": null}], "bbox": {"p1": [109.55018667567174, 197.78389482909725], "p2": [172.97030749266386, 197.78389482909725], "p3": [176.32484902887003, 222.73473561203636], "p4": [112.06066802999278, 222.73473561203636]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 15, "header": false, "content": [{"bbox": null, "direction": null, "text": "5", "score": null}], "bbox": {"p1": [129.27272727272728, 335.46697247706436], "p2": [192.18616088132111, 335.46697247706436], "p3": [192.18616088132111, 358.3339449541283], "p4": [129.27272727272728, 358.3339449541283]}, "lloc": {"start_row": 11, "end_row": 11, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 16, "header": false, "content": [{"bbox": null, "direction": null, "text": "7.5", "score": null}], "bbox": {"p1": [129.27272727272728, 358.3339449541283], "p2": [192.18616088132111, 358.3339449541283], "p3": [192.18616088132111, 383.76754503162636], "p4": [131.33333333333334, 383.76754503162636]}, "lloc": {"start_row": 12, "end_row": 12, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 17, "header": false, "content": [{"bbox": null, "direction": null, "text": "10", "score": null}], "bbox": {"p1": [112.06066802999278, 222.73473561203636], "p2": [176.32484902887003, 222.73473561203636], "p3": [179.62735822368495, 246.85463044113348], "p4": [115.2526867627781, 246.85463044113348]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 18, "header": false, "content": [{"bbox": null, "direction": null, "text": "7.5", "score": null}], "bbox": {"p1": [100.8408997955008, 119.97659669574774], "p2": [168.54534677444354, 119.97659669574774], "p3": [168.54534677444354, 144.68952041648262], "p4": [103.13633265166997, 144.68952041648262]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 19, "header": false, "content": [{"bbox": null, "direction": null, "text": "F4", "score": null}], "bbox": {"p1": [40.0, 172.57704254137906], "p2": [106.84662576687106, 172.57704254137906], "p3": [109.55018667567174, 197.78389482909725], "p4": [43.51124744376284, 197.78389482909725]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 20, "header": false, "content": [{"bbox": null, "direction": null, "text": "15", "score": null}], "bbox": {"p1": [126.30581039755361, 455.0397554263427], "p2": [188.69463123628725, 455.0397554263427], "p3": [188.69463123628725, 477.4605363198377], "p4": [122.52508960573475, 477.4605363198376]}, "lloc": {"start_row": 16, "end_row": 16, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 21, "header": false, "content": [{"bbox": null, "direction": null, "text": "Master window control switch, DC/AC inverter module, Roof opening panel module,\nPower sliding window switch, telescoping exterior rear view mirror switch", "score": null}], "bbox": {"p1": [168.54534677444354, 636.9739196777343], "p2": [1023.814697265625, 608.8881158730637], "p3": [1025.29345703125, 654.2404174804688], "p4": [168.54534677444354, 681.8348811438323]}, "lloc": {"start_row": 23, "end_row": 23, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 22, "header": false, "content": [{"bbox": null, "direction": null, "text": "10", "score": null}], "bbox": {"p1": [129.27272727272728, 431.0015747771661], "p2": [192.18616088132111, 431.0015747771661], "p3": [188.69463123628725, 455.0397554263427], "p4": [126.30581039755361, 455.0397554263427]}, "lloc": {"start_row": 15, "end_row": 15, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 23, "header": false, "content": [{"bbox": null, "direction": null, "text": "10", "score": null}], "bbox": {"p1": [115.2526867627781, 246.85463044113348], "p2": [179.62735822368495, 246.85463044113348], "p3": [182.22501316400826, 268.16359918200396], "p4": [118.44325153374257, 268.16359918200396]}, "lloc": {"start_row": 7, "end_row": 7, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 24, "header": false, "content": [{"bbox": null, "direction": null, "text": "F18", "score": null}], "bbox": {"p1": [58.61162079510723, 503.2293577981651], "p2": [122.52508960573475, 503.2293577981651], "p3": [118.44325153374257, 528.6019367991845], "p4": [57.0, 528.6019367991845]}, "lloc": {"start_row": 18, "end_row": 18, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 25, "header": false, "content": [{"bbox": null, "direction": null, "text": "-", "score": null}], "bbox": {"p1": [100.8408997955008, 93.81863503885762], "p2": [165.638391345796, 93.81863503885762], "p3": [168.54534677444354, 119.97659669574774], "p4": [100.8408997955008, 119.97659669574774]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 26, "header": false, "content": [{"bbox": null, "direction": null, "text": "5", "score": null}], "bbox": {"p1": [122.52508960573475, 477.4605363198376], "p2": [188.69463123628725, 477.4605363198377], "p3": [184.84943918933936, 503.2293577981651], "p4": [122.52508960573475, 503.2293577981651]}, "lloc": {"start_row": 17, "end_row": 17, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 27, "header": false, "content": [{"bbox": null, "direction": null, "text": "7.5", "score": null}], "bbox": {"p1": [131.33333333333334, 383.76754503162636], "p2": [192.18616088132111, 383.76754503162636], "p3": [192.18616088132111, 408.08125253439187], "p4": [131.33333333333334, 408.08125253439187]}, "lloc": {"start_row": 13, "end_row": 13, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 28, "header": false, "content": [{"bbox": null, "direction": null, "text": "F16", "score": null}], "bbox": {"p1": [65.30581039755361, 455.0397554263427], "p2": [126.30581039755361, 455.0397554263427], "p3": [122.52508960573475, 477.4605363198376], "p4": [62.5, 477.4605363198376]}, "lloc": {"start_row": 16, "end_row": 16, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 29, "header": false, "content": [{"bbox": null, "direction": null, "text": "F2", "score": null}], "bbox": {"p1": [35.715746421267795, 119.97659669574774], "p2": [100.8408997955008, 119.97659669574774], "p3": [103.13633265166997, 144.68952041648262], "p4": [37.40899795501082, 144.68952041648262]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 30, "header": false, "content": [{"bbox": null, "direction": null, "text": "F7", "score": null}], "bbox": {"p1": [50.02061875011714, 246.85463044113348], "p2": [115.2526867627781, 246.85463044113348], "p3": [118.44325153374257, 268.16359918200396], "p4": [55.10224948875293, 268.16359918200396]}, "lloc": {"start_row": 7, "end_row": 7, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 31, "header": false, "content": [{"bbox": null, "direction": null, "text": "F3", "score": null}], "bbox": {"p1": [37.40899795501082, 144.68952041648262], "p2": [103.13633265166997, 144.68952041648262], "p3": [106.84662576687106, 172.57704254137906], "p4": [40.0, 172.57704254137906]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 32, "header": false, "content": [{"bbox": null, "direction": null, "text": "F6", "score": null}], "bbox": {"p1": [46.61349693251486, 222.73473561203636], "p2": [112.06066802999278, 222.73473561203636], "p3": [115.2526867627781, 246.85463044113348], "p4": [50.02061875011714, 246.85463044113348]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 33, "header": false, "content": [{"bbox": null, "direction": null, "text": "F13", "score": null}], "bbox": {"p1": [70.0, 383.76754503162636], "p2": [131.33333333333334, 383.76754503162636], "p3": [131.33333333333334, 408.08125253439187], "p4": [69.5, 408.08125253439187]}, "lloc": {"start_row": 13, "end_row": 13, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 34, "header": false, "content": [{"bbox": null, "direction": null, "text": "F12", "score": null}], "bbox": {"p1": [68.5, 358.3339449541283], "p2": [131.33333333333334, 358.3339449541283], "p3": [131.33333333333334, 383.76754503162636], "p4": [70.0, 383.76754503162636]}, "lloc": {"start_row": 12, "end_row": 12, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 35, "header": false, "content": [{"bbox": null, "direction": null, "text": "F1", "score": null}], "bbox": {"p1": [35.59100204499009, 93.81863503885762], "p2": [100.8408997955008, 93.81863503885762], "p3": [100.8408997955008, 119.97659669574774], "p4": [35.715746421267795, 119.97659669574774]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 36, "header": false, "content": [{"bbox": null, "direction": null, "text": "10", "score": null}], "bbox": {"p1": [131.33333333333334, 408.08125253439187], "p2": [192.18616088132111, 408.08125253439187], "p3": [192.18616088132111, 431.0015747771661], "p4": [129.27272727272728, 431.0015747771661]}, "lloc": {"start_row": 14, "end_row": 14, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 37, "header": false, "content": [{"bbox": null, "direction": null, "text": "F14", "score": null}], "bbox": {"p1": [69.5, 408.08125253439187], "p2": [131.33333333333334, 408.08125253439187], "p3": [129.27272727272728, 431.0015747771661], "p4": [68.38121546961338, 431.0015747771661]}, "lloc": {"start_row": 14, "end_row": 14, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 38, "header": false, "content": [{"bbox": null, "direction": null, "text": "F17", "score": null}], "bbox": {"p1": [62.5, 477.4605363198376], "p2": [122.52508960573475, 477.4605363198376], "p3": [122.52508960573475, 503.2293577981651], "p4": [58.61162079510723, 503.2293577981651]}, "lloc": {"start_row": 17, "end_row": 17, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 39, "header": false, "content": [{"bbox": null, "direction": null, "text": "10", "score": null}], "bbox": {"p1": [118.44325153374257, 268.16359918200396], "p2": [182.22501316400826, 268.16359918200396], "p3": [184.84943918933936, 291.65626911314973], "p4": [122.52508960573475, 291.65626911314973]}, "lloc": {"start_row": 8, "end_row": 8, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 40, "header": false, "content": [{"bbox": null, "direction": null, "text": "10", "score": null}], "bbox": {"p1": [122.52508960573475, 291.65626911314973], "p2": [184.84943918933936, 291.65626911314973], "p3": [188.69463123628725, 314.2562691131499], "p4": [126.30581039755361, 314.2562691131499]}, "lloc": {"start_row": 9, "end_row": 9, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 41, "header": false, "content": [{"bbox": null, "direction": null, "text": "F15", "score": null}], "bbox": {"p1": [68.38121546961338, 431.0015747771661], "p2": [129.27272727272728, 431.0015747771661], "p3": [126.30581039755361, 455.0397554263427], "p4": [65.30581039755361, 455.0397554263427]}, "lloc": {"start_row": 15, "end_row": 15, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 42, "header": true, "content": [{"bbox": null, "direction": null, "text": "<PERSON><PERSON>", "score": null}], "bbox": {"p1": [35.0, 66.0], "p2": [100.8408997955008, 65.40899795500991], "p3": [100.8408997955008, 93.81863503885762], "p4": [35.59100204499009, 93.81863503885762]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 43, "header": false, "content": [{"bbox": null, "direction": null, "text": "5", "score": null}], "bbox": {"p1": [126.30581039755361, 314.2562691131499], "p2": [188.69463123628725, 314.2562691131499], "p3": [192.18616088132111, 335.46697247706436], "p4": [129.27272727272728, 335.46697247706436]}, "lloc": {"start_row": 10, "end_row": 10, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 44, "header": false, "content": [{"bbox": null, "direction": null, "text": "F5", "score": null}], "bbox": {"p1": [43.51124744376284, 197.78389482909725], "p2": [109.55018667567174, 197.78389482909725], "p3": [112.06066802999278, 222.73473561203636], "p4": [46.61349693251486, 222.73473561203636]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 45, "header": false, "content": [{"bbox": null, "direction": null, "text": "F11", "score": null}], "bbox": {"p1": [66.5, 335.46697247706436], "p2": [129.27272727272728, 335.46697247706436], "p3": [129.27272727272728, 358.3339449541283], "p4": [68.5, 358.3339449541283]}, "lloc": {"start_row": 11, "end_row": 11, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 46, "header": false, "content": [{"bbox": null, "direction": null, "text": "F10", "score": null}], "bbox": {"p1": [62.5, 314.2562691131499], "p2": [126.30581039755361, 314.2562691131499], "p3": [129.27272727272728, 335.46697247706436], "p4": [66.5, 335.46697247706436]}, "lloc": {"start_row": 10, "end_row": 10, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 47, "header": false, "content": [{"bbox": null, "direction": null, "text": "Clockspring / Steering Angle Sensor Module (SASM)", "score": null}], "bbox": {"p1": [176.32484902887003, 551.3419799804688], "p2": [1023.9288940429688, 533.0241088867188], "p3": [1024.5552978515625, 561.9879760742188], "p4": [176.32484902887003, 581.5711899503655]}, "lloc": {"start_row": 20, "end_row": 20, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 48, "header": false, "content": [{"bbox": null, "direction": null, "text": "Steering Column Control Module (SCCM), Instrument Panel Cluster (IPC), Gateway module", "score": null}], "bbox": {"p1": [192.18616088132111, 383.76754503162636], "p2": [1024.0, 370.3205042129907], "p3": [1024.3699951171875, 394.7255859375], "p4": [192.18616088132111, 408.08125253439187]}, "lloc": {"start_row": 13, "end_row": 13, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 49, "header": false, "content": [{"bbox": null, "direction": null, "text": "Front Controls Interface <PERSON>le (FCIM)", "score": null}], "bbox": {"p1": [192.18616088132111, 358.3339449541283], "p2": [1024.0, 345.92307692307713], "p3": [1024.0, 370.3205042129907], "p4": [192.18616088132111, 383.76754503162636]}, "lloc": {"start_row": 12, "end_row": 12, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 50, "header": false, "content": [{"bbox": null, "direction": null, "text": "In-vehicle temperature/humidity sensor, Head Up Display (HUD) module", "score": null}], "bbox": {"p1": [176.32484902887003, 578.1611328125], "p2": [1023.92529296875, 558.7352294921875], "p3": [1024.600830078125, 588.268310546875], "p4": [176.32484902887003, 608.8881158730637]}, "lloc": {"start_row": 21, "end_row": 21, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 51, "header": false, "content": [{"bbox": null, "direction": null, "text": "Decklid/liftgate relay", "score": null}], "bbox": {"p1": [188.69463123628725, 455.0397554263427], "p2": [1024.2007446289062, 439.99676513671875], "p3": [1023.9895324707031, 462.10803340031543], "p4": [188.69463123628725, 477.4605363198377]}, "lloc": {"start_row": 16, "end_row": 16, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 52, "header": false, "content": [{"bbox": null, "direction": null, "text": "F9", "score": null}], "bbox": {"p1": [59.0, 291.65626911314973], "p2": [122.52508960573475, 291.65626911314973], "p3": [126.30581039755361, 314.2562691131499], "p4": [62.5, 314.2562691131499]}, "lloc": {"start_row": 9, "end_row": 9, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 53, "header": false, "content": [{"bbox": null, "direction": null, "text": "Tow haul switch", "score": null}], "bbox": {"p1": [179.62735822368495, 526.1160215791942], "p2": [1024.2037963867188, 509.29637145996094], "p3": [1024.5025634765625, 536.3017578125], "p4": [179.62735822368495, 554.4573646102112]}, "lloc": {"start_row": 19, "end_row": 19, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 54, "header": false, "content": [{"bbox": null, "direction": null, "text": "F8", "score": null}], "bbox": {"p1": [55.10224948875293, 268.16359918200396], "p2": [118.44325153374257, 268.16359918200396], "p3": [122.52508960573475, 291.65626911314973], "p4": [59.0, 291.65626911314973]}, "lloc": {"start_row": 8, "end_row": 8, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 55, "header": false, "content": [{"bbox": null, "direction": null, "text": "Powertrain Control Module (PCM), Trailer Brake Control (TBM) module", "score": null}], "bbox": {"p1": [172.97030749266386, 172.57704254137903], "p2": [1024.0836181640625, 166.1303038057565], "p3": [1024.0, 188.67948717948684], "p4": [172.97030749266386, 197.78389482909725]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 56, "header": false, "content": [{"bbox": null, "direction": null, "text": "Not used", "score": null}], "bbox": {"p1": [182.22501316400826, 268.16359918200396], "p2": [1024.0, 256.24358974358984], "p3": [1024.0, 278.96153846153857], "p4": [184.84943918933936, 291.65626911314973]}, "lloc": {"start_row": 8, "end_row": 8, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 57, "header": false, "content": [{"bbox": null, "direction": null, "text": "Not used", "score": null}], "bbox": {"p1": [172.97030749266386, 197.78389482909725], "p2": [1024.0, 188.67948717948684], "p3": [1024.0, 212.28205128205127], "p4": [176.32484902887003, 222.73473561203636]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 58, "header": false, "content": [{"bbox": null, "direction": null, "text": "Driver/Fuel flap unlock relay", "score": null}], "bbox": {"p1": [168.54534677444354, 144.68952041648262], "p2": [1023.9804077148438, 138.34506225585938], "p3": [1024.0836181640625, 166.1303038057565], "p4": [168.54534677444354, 172.57704254137906]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 59, "header": false, "content": [{"bbox": null, "direction": null, "text": "Head Up Display (HUD) module", "score": null}], "bbox": {"p1": [184.84943918933936, 477.4605363198376], "p2": [1023.9895324707031, 462.10803340031543], "p3": [1024.1871948242188, 485.8381652832031], "p4": [184.84943918933936, 501.30934143066406]}, "lloc": {"start_row": 17, "end_row": 17, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 60, "header": false, "content": [{"bbox": null, "direction": null, "text": "Ignition switch-push button start, Ignition switch", "score": null}], "bbox": {"p1": [182.22501316400826, 501.30934143066406], "p2": [1024.1871948242188, 485.8381652832031], "p3": [1024.2037963867188, 509.29637145996094], "p4": [182.22501316400826, 526.1160215791942]}, "lloc": {"start_row": 18, "end_row": 18, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 61, "header": false, "content": [{"bbox": null, "direction": null, "text": "Intrusion sensor", "score": null}], "bbox": {"p1": [192.18616088132111, 335.46697247706436], "p2": [1023.6410256410254, 323.96153846153857], "p3": [1024.0, 345.92307692307713], "p4": [192.18616088132111, 358.3339449541283]}, "lloc": {"start_row": 11, "end_row": 11, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 62, "header": false, "content": [{"bbox": null, "direction": null, "text": "Customer access, Up fitter switch, Up fitter relay box", "score": null}], "bbox": {"p1": [172.97030749266386, 608.8881158730637], "p2": [1023.9269409179688, 585.4173583984375], "p3": [1024.6575927734375, 614.0986328125], "p4": [172.97030749266386, 636.9739196777343]}, "lloc": {"start_row": 22, "end_row": 22, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 63, "header": false, "content": [{"bbox": null, "direction": null, "text": "Not used", "score": null}], "bbox": {"p1": [176.32484902887003, 222.73473561203636], "p2": [1024.0, 212.28205128205127], "p3": [1024.0, 235.56410256410254], "p4": [179.62735822368495, 246.85463044113348]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 64, "header": false, "content": [{"bbox": null, "direction": null, "text": "Gateway module", "score": null}], "bbox": {"p1": [188.69463123628725, 428.7791748046875], "p2": [1024.1850280761719, 415.2404022216797], "p3": [1024.2007446289062, 439.99676513671875], "p4": [188.69463123628725, 455.0397554263427]}, "lloc": {"start_row": 15, "end_row": 15, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 65, "header": false, "content": [{"bbox": null, "direction": null, "text": "Brake Pedal Position (BPP) switch", "score": null}], "bbox": {"p1": [184.84943918933936, 291.65626911314973], "p2": [1024.0, 278.96153846153857], "p3": [1024.0, 301.28205128205127], "p4": [188.69463123628725, 314.2562691131499]}, "lloc": {"start_row": 9, "end_row": 9, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 66, "header": false, "content": [{"bbox": null, "direction": null, "text": "Battery Energy Control Module B (BECMB)", "score": null}], "bbox": {"p1": [192.18616088132111, 408.08125253439187], "p2": [1023.970703125, 392.3188781738281], "p3": [1024.1850280761719, 415.2404022216797], "p4": [192.18616088132111, 431.0015747771661]}, "lloc": {"start_row": 14, "end_row": 14, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 67, "header": false, "content": [{"bbox": null, "direction": null, "text": "Not used", "score": null}], "bbox": {"p1": [179.62735822368495, 246.85463044113348], "p2": [1024.0, 235.56410256410254], "p3": [1024.0, 256.24358974358984], "p4": [182.22501316400826, 268.16359918200396]}, "lloc": {"start_row": 7, "end_row": 7, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 68, "header": false, "content": [{"bbox": null, "direction": null, "text": "Not used", "score": null}], "bbox": {"p1": [188.69463123628725, 314.2562691131499], "p2": [1024.0, 301.28205128205127], "p3": [1023.6410256410254, 323.96153846153857], "p4": [192.18616088132111, 335.46697247706436]}, "lloc": {"start_row": 10, "end_row": 10, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 69, "header": false, "content": [{"bbox": null, "direction": null, "text": "Driver <PERSON>t <PERSON>le (DSM), Seat control switch, driver side front", "score": null}], "bbox": {"p1": [168.54534677444354, 119.97659669574774], "p2": [1023.9786987304688, 114.83676147460938], "p3": [1024.095458984375, 141.30535888671875], "p4": [168.54534677444354, 144.68952041648262]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 70, "header": true, "content": [{"bbox": null, "direction": null, "text": "Circuits protected", "score": null}], "bbox": {"p1": [165.638391345796, 61.481201171875], "p2": [1024.16845703125, 65.95165252685547], "p3": [1024.0, 98.33222961425781], "p4": [165.638391345796, 93.81863503885762]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 71, "header": false, "content": [{"bbox": null, "direction": null, "text": "Not used", "score": null}], "bbox": {"p1": [165.638391345796, 93.81863503885762], "p2": [1024.00537109375, 93.81863503885762], "p3": [1024.0, 119.97659669574774], "p4": [165.638391345796, 119.97659669574774]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}