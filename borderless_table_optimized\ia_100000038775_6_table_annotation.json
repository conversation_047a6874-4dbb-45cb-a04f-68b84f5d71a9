{"table_ind": "ia_100000038775_6", "image_path": "ia_100000038775_6.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": true, "content": [{"bbox": null, "direction": null, "text": "Debit Balances (#)", "score": null}], "bbox": {"p1": [666.*************, 42.916666666666515], "p2": [841.1103651258682, 37.666666666666515], "p3": [843.0, 72.35664367675781], "p4": [666.*************, 80.**************]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "2,50,000\n...\n12,000\n...\n8,000\n60,000\n2,000\n1,200\n8,000\n1,15,000\n...\n6,000\n15,000\n4,000\n...\n30,000", "score": null}], "bbox": {"p1": [666.*************, 80.**************], "p2": [865.1981811523438, 72.35664367675781], "p3": [885.3120727539062, 554.720947265625], "p4": [684.515625, 562.8424682617188]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": true, "content": [{"bbox": null, "direction": null, "text": "Cred## B###", "score": null}], "bbox": {"p1": [841.1103651258682, 30.929170608520508], "p2": [961.3045043945312, 53.86869812011719], "p3": [953.5670776367188, 94.20780944824219], "p4": [833.9716186523438, 72.35664367675781]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "5,00,000\n...\n10,000\n...\n...\n...\n...\n...\n...\n1,00,000\n...\n...\n...\n2,000\n...", "score": null}], "bbox": {"p1": [841.1103651258682, 72.35664367675781], "p2": [958.8693237304688, 62.51283264160156], "p3": [1003.*************, 555.5929565429688], "p4": [885.3120727539062, 566.2425537109375]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [{"bbox": null, "direction": null, "text": "Perchasas\nSales\nReturns Inwand\nReturns Outward\nCarriage\nWages\nTrade Expenses\nInsurance\nRepairs #/2\nDebtors\nCreditors\nPrinting and Stationery #/2\nAdvertisement #/2\nBills Receivable\nBills Payable\nOpening Stock", "score": null}], "bbox": {"p1": [-15.***************, 104.**************], "p2": [666.*************, 80.**************], "p3": [684.515625, 554.*************], "p4": [2.*************, 579.*************]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": true, "content": [{"bbox": null, "direction": null, "text": "###s of Accounts", "score": null}], "bbox": {"p1": [67.**************, 69.**************], "p2": [666.*************, 40.**************], "p3": [666.*************, 80.**************], "p4": [69.**************, 109.**************]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}