{"table_ind": "5494457070", "image_path": "5494457070.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": false, "content": [{"bbox": null, "direction": null, "text": "b) Define Universal gate. Explain how NOR gate operate\nas Basic gate with necessary realization technique.", "score": null}], "bbox": {"p1": [112.0, 405.0827067669173], "p2": [801.1243712070318, 393.4417862838918], "p3": [801.1243712070318, 454.54827500620496], "p4": [114.83333333333333, 466.1666666666667]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": true, "content": [{"bbox": null, "direction": null, "text": "a)Define Signal. Differentiate between analog and\ndigital signal with suitable example.", "score": null}], "bbox": {"p1": [105.8509450912477, 64.5443115234375], "p2": [801.1243712070318, 59.93829345703125], "p3": [801.1243712070318, 123.9979960123698], "p4": [105.8509450912477, 128.43618477433316]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": false, "content": [{"bbox": null, "direction": null, "text": "a) Explain about AND and OR gate with necessary truth\ntable , symbol and logical expression.", "score": null}], "bbox": {"p1": [109.76109537299332, 342.6166194523133], "p2": [801.1243712070318, 333.4068442620557], "p3": [801.1243712070318, 393.4417862838918], "p4": [112.0, 405.0827067669173]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "b) Convert the following number system.\nⅰ.(2564.87D)<sub>16</sub>= (?)<sub>2</sub>\nⅱ.(4432.123)<sub>8</sub>= (?)<sub>16</sub>\nⅲ.(111101010*11010)<sub>2</sub>\nⅳ. (1101001.110)<sub>2</sub>/ (110)<sub>2</sub>", "score": null}], "bbox": {"p1": [105.8509450912477, 128.43618477433316], "p2": [801.1243712070318, 118.81493377685547], "p3": [801.1243712070318, 271.3753323752823], "p4": [105.8509450912477, 280.82208606406584]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": true, "content": [{"bbox": null, "direction": null, "text": "[2+4=6]", "score": null}], "bbox": {"p1": [801.1243712070318, 61.0], "p2": [927.0, 59.0], "p3": [928.0, 122.0], "p4": [801.1243712070318, 123.9979960123698]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": false, "content": [{"bbox": null, "direction": null, "text": "[3+3]", "score": null}], "bbox": {"p1": [801.1243712070318, 333.4068442620557], "p2": [928.7590361445782, 333.4068442620557], "p3": [927.7177033492817, 393.4417862838918], "p4": [801.1243712070318, 393.4417862838918]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": false, "content": [{"bbox": null, "direction": null, "text": "[1+5]", "score": null}], "bbox": {"p1": [801.1243712070318, 393.4417862838918], "p2": [927.7177033492817, 393.4417862838918], "p3": [929.2352941176478, 454.54827500620496], "p4": [801.1243712070318, 454.54827500620496]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": false, "content": [{"bbox": null, "direction": null, "text": "c)Subtract the following (1110101C)<sub>2</sub> from (111110000)<sub>2</sub>\nusing 2's Compliment.", "score": null}], "bbox": {"p1": [109.76109537299332, 280.82208606406584], "p2": [798.8583690987125, 271.3753323752823], "p3": [801.1243712070318, 333.4068442620557], "p4": [109.76109537299332, 342.6166194523133]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": false, "content": [{"bbox": null, "direction": null, "text": "[1x4=4]", "score": null}], "bbox": {"p1": [801.1243712070318, 123.9979960123698], "p2": [927.0, 122.0], "p3": [927.0, 271.3753323752823], "p4": [798.8583690987125, 271.3753323752823]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 9, "header": false, "content": [{"bbox": null, "direction": null, "text": "[2]", "score": null}], "bbox": {"p1": [798.8583690987125, 271.3753323752823], "p2": [927.0, 271.3753323752823], "p3": [928.7590361445782, 333.4068442620557], "p4": [801.1243712070318, 333.4068442620557]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 10, "header": false, "content": [{"bbox": null, "direction": null, "text": "[6]", "score": null}], "bbox": {"p1": [801.1243712070318, 454.54827500620496], "p2": [929.2352941176478, 454.54827500620496], "p3": [930.0, 503.0], "p4": [802.8987341772154, 502.6835443037976]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 11, "header": false, "content": [{"bbox": null, "direction": null, "text": "a) Simplify the following expression using K- map.", "score": null}], "bbox": {"p1": [114.83333333333333, 466.1666666666667], "p2": [801.1243712070318, 454.54827500620496], "p3": [802.8987341772154, 502.6835443037976], "p4": [114.83333333333333, 502.0]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 12, "header": false, "content": [], "bbox": {"p1": [59.37593984962405, 405.0827067669173], "p2": [112.0, 405.0827067669173], "p3": [114.83333333333333, 466.1666666666667], "p4": [61.0, 466.1666666666667]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 13, "header": false, "content": [], "bbox": {"p1": [54.525394045534085, 128.43618477433316], "p2": [105.8509450912477, 128.43618477433316], "p3": [109.76109537299332, 342.6166194523133], "p4": [58.0, 342.6166194523133]}, "lloc": {"start_row": 1, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 14, "header": false, "content": [{"bbox": null, "direction": null, "text": "2.", "score": null}], "bbox": {"p1": [58.0, 342.6166194523133], "p2": [109.76109537299332, 342.6166194523133], "p3": [112.0, 405.0827067669173], "p4": [59.37593984962405, 405.0827067669173]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 15, "header": false, "content": [{"bbox": null, "direction": null, "text": "3.", "score": null}], "bbox": {"p1": [61.0, 466.1666666666667], "p2": [114.83333333333333, 466.1666666666667], "p3": [114.83333333333333, 503.0], "p4": [61.0, 502.0]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 16, "header": true, "content": [{"bbox": null, "direction": null, "text": "1.", "score": null}], "bbox": {"p1": [53.0, 66.0], "p2": [105.8509450912477, 67.0], "p3": [105.8509450912477, 128.43618477433316], "p4": [54.525394045534085, 128.43618477433316]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}