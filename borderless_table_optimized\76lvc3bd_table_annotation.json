{"table_ind": 0, "image_path": "", "type": 1, "cells": [{"cell_ind": 0, "header": false, "content": [{"bbox": null, "direction": null, "text": "More costs - insurance", "score": null}], "bbox": {"p1": [432.4627352236373, 210.24449877750612], "p2": [666.0, 196.0], "p3": [666.8486562942012, 220.0], "p4": [432.4627352236373, 235.00497512437778]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 0, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "More traffic jams", "score": null}], "bbox": {"p1": [432.4627352236373, 235.00497512437778], "p2": [666.8486562942012, 220.0], "p3": [667.0, 244.56577086280095], "p4": [434.894936708861, 259.83867131084907]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 0, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": true, "content": [{"bbox": null, "direction": null, "text": "Pros", "score": null}], "bbox": {"p1": [35.0, 149.0], "p2": [426.7753868738966, 134.29910269192396], "p3": [426.7753868738966, 159.6908809891811], "p4": [35.642710472279305, 178.38398357289543]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "High risk - accidents", "score": null}], "bbox": {"p1": [428.4000000000001, 184.5], "p2": [667.0, 171.71509971509977], "p3": [666.0, 196.0], "p4": [432.4627352236373, 210.24449877750612]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 0, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [{"bbox": null, "direction": null, "text": "Easier and smoother life", "score": null}], "bbox": {"p1": [434.894936708861, 259.83867131084907], "p2": [435.0202531645572, 285.0475320703424], "p3": [40.340136054422146, 314.51020408163276], "p4": [39.00671140939585, 286.12993798317916]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": false, "content": [{"bbox": null, "direction": null, "text": "Greater independence", "score": null}], "bbox": {"p1": [35.642710472279305, 178.38398357289543], "p2": [426.7753868738966, 159.6908809891811], "p3": [428.4000000000001, 184.5], "p4": [35.642710472279305, 204.02669404517428]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": false, "content": [{"bbox": null, "direction": null, "text": "Violating rules", "score": null}], "bbox": {"p1": [434.894936708861, 259.83867131084907], "p2": [667.0, 244.56577086280095], "p3": [666.6973125884019, 268.8585572842999], "p4": [435.0202531645572, 285.0475320703424]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 0, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": false, "content": [{"bbox": null, "direction": null, "text": "Lack of experience", "score": null}], "bbox": {"p1": [426.7753868738966, 159.6908809891811], "p2": [667.0, 147.8850574712642], "p3": [667.0, 171.71509971509977], "p4": [428.4000000000001, 184.5]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 0, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": false, "content": [{"bbox": null, "direction": null, "text": "More driving skills", "score": null}], "bbox": {"p1": [37.556567002791326, 232.22379527413887], "p2": [432.4627352236373, 210.24449877750612], "p3": [432.4627352236373, 235.00497512437778], "p4": [38.657718120805384, 259.83867131084907]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 9, "header": false, "content": [{"bbox": null, "direction": null, "text": "Increased responsibility", "score": null}], "bbox": {"p1": [35.642710472279305, 204.02669404517428], "p2": [428.4000000000001, 184.5], "p3": [432.4627352236373, 210.24449877750612], "p4": [37.556567002791326, 232.22379527413887]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 10, "header": true, "content": [{"bbox": null, "direction": null, "text": "Cons", "score": null}], "bbox": {"p1": [426.7753868738966, 134.29910269192396], "p2": [666.0, 125.0], "p3": [667.0, 147.8850574712642], "p4": [426.7753868738966, 159.6908809891811]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 0, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 11, "header": false, "content": [{"bbox": null, "direction": null, "text": "Self-confidence", "score": null}], "bbox": {"p1": [38.657718120805384, 259.83867131084907], "p2": [432.4627352236373, 235.00497512437778], "p3": [434.894936708861, 259.83867131084907], "p4": [39.00671140939585, 286.12993798317916]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}