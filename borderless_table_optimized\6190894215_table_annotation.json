{"table_ind": "6190894215", "image_path": "6190894215.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": false, "content": [{"bbox": null, "direction": null, "text": "INTRODUCE BRAIDY\n— Braidy Tool Box Activity\npage 32\n— Braidy Tool Box Activity\npage 33\nMaterials needed:\n— Braidy doll\n— Braidy facial features\n— Braidy teachers'\nmanual\n— Copy and distribute\nParents/Family Letter\nfrom the manual\nfound on page 41", "score": null}], "bbox": {"p1": [64.62751770019531, 246.30985622634762], "p2": [203.13474859771046, 248.91544796953895], "p3": [195.7922592163086, 533.5574951171875], "p4": [57.437644958496094, 530.0634765625]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "CHARACTER AWARENESS\n— Character Song.\npage 33\n— Character List\n— Character Drawing\n— Simple sentence\ndevelopment using \"is\"\nMaterials needed:\n— Braidy doll\n— Braidy teachers'\nmanual\n— Character pattern.\npage 138—— Character\npicture (one for\neach child)", "score": null}], "bbox": {"p1": [192.0016632080078, 248.91544796953895], "p2": [330.0546126391111, 246.30985622634762], "p3": [333.92710876464844, 522.6107177734375], "p4": [195.7922592163086, 524.5623779296875]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": false, "content": [{"bbox": null, "direction": null, "text": "CHARACTER EXPANSION\n— Character lcon/\nvocabulary connection\n— List/Categoriation\n— Simpie sentence\ndevelopment using\"are\"\nMaterials needed:\n— Braidy doll\n— Character pattern,\npage 138\n— Chart paper/markers", "score": null}], "bbox": {"p1": [324.9819030761719, 246.30985622634762], "p2": [460.8770446777344, 242.302591204176], "p3": [469.79359436035156, 513.4690551757812], "p4": [333.92710876464844, 517.932861328125]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "CHARACTER EXPANSION\nCHARACTER\nDIFFERENTIATION\n— Character Vocabulary\nmale/female (he/she)\n— Descriptive Vocabulary\n(simple noun phrases)\n— Facial Expression\nVocabularyfuniversal\nemotions\nMaterials needed\n— Braidy doli\n— Braidy facial features.\n(cards)", "score": null}], "bbox": {"p1": [454.7402227202965, 242.302591204176], "p2": [588.84814453125, 234.5976281097378], "p3": [604.3534240722656, 508.4013671875], "p4": [469.79359436035156, 515.81494140625]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [{"bbox": null, "direction": null, "text": "PHONOLOGICAL\nAWARENESS ACTIVITY\n— Number of words in \na sentence\n— Simple sentence\ndevelopment (nouns,\npronouns, adjectives)\nMaterials needed:\n— Action Beads\nnumbered 1-6, page 141\n— Optional sentence strips", "score": null}], "bbox": {"p1": [577.3748950507555, 238.77205920612172], "p2": [711.6865844726562, 225.2864990234375], "p3": [739.6011352539062, 502.664794921875], "p4": [604.3534240722656, 516.236083984375]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": true, "content": [{"bbox": null, "direction": null, "text": "Lessons 3-5", "score": null}], "bbox": {"p1": [203.13474859771048, 232.95177563935385], "p2": [330.0546126391111, 229.80974920942435], "p3": [330.0546126391111, 246.30985622634762], "p4": [203.13474859771046, 248.91544796953895]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": true, "content": [{"bbox": null, "direction": null, "text": "Lessons 1-2", "score": null}], "bbox": {"p1": [72.64915310412198, 234.72690006454195], "p2": [203.13474859771048, 232.95177563935385], "p3": [203.13474859771046, 248.91544796953895], "p4": [71.80552612918291, 252.74229387902778]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": true, "content": [{"bbox": null, "direction": null, "text": "Lessons 6-7", "score": null}], "bbox": {"p1": [330.0546126391111, 229.80974920942435], "p2": [454.7402227202965, 226.38433579472348], "p3": [454.7402227202965, 242.302591204176], "p4": [330.0546126391111, 246.30985622634762]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": true, "content": [{"bbox": null, "direction": null, "text": "Lessons 8-10", "score": null}], "bbox": {"p1": [454.7402227202965, 226.38433579472348], "p2": [577.3748950507555, 222.62361851090554], "p3": [577.3748950507555, 238.77205920612172], "p4": [454.7402227202965, 242.302591204176]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 9, "header": true, "content": [{"bbox": null, "direction": null, "text": "Lessons 11-12", "score": null}], "bbox": {"p1": [577.3748950507555, 222.62361851090554], "p2": [701.1408168166345, 218.4983316078583], "p3": [702.6400452798821, 234.5976281097378], "p4": [577.3748950507555, 238.77205920612172]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}