{"table_ind": "6193035822", "image_path": "6193035822.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": false, "content": [{"bbox": null, "direction": null, "text": "MERCERIE:<PERSON><PERSON>. Ve###\nmanches raglan) -###\n0.95m d'élastique de###", "score": null}], "bbox": {"p1": [1338.4774028676743, 1076.9867347565712], "p2": [1599.0, 1071.0], "p3": [1597.0, 1171.0], "p4": [1338.4774028676743, 1181.333333333333]}, "lloc": {"start_row": 9, "end_row": 9, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "TISSUS CONSEILL\nEtamine · Chambr###\nmétallisé non extens###\nN.B. Tous vêtemen###", "score": null}], "bbox": {"p1": [1338.4774028676743, 945.140889028485], "p2": [1598.5, 942.0], "p3": [1599.0, 1071.0], "p4": [1338.4774028676743, 1076.9867347565712]}, "lloc": {"start_row": 8, "end_row": 8, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": false, "content": [{"bbox": null, "direction": null, "text": "MESURES\nTaille\nTour de poitrine 7\nTour de taille 5\nTour de hanches ###\nLongueur dos ###", "score": null}], "bbox": {"p1": [1334.3129541592339, 178.91337741641533], "p2": [1600.0, 186.21259842519703], "p3": [1599.7874015748039, 364.20638209697427], "p4": [1342.9508962742411, 354.96113472285197]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "*A sens ***Avec o###\npelucheux et les im###\nles rayures ou les é###", "score": null}], "bbox": {"p1": [1342.9508962742411, 840.2378749763393], "p2": [1599.934579439252, 837.1962616822439], "p3": [1598.5, 942.0], "p4": [1338.4774028676743, 945.140889028485]}, "lloc": {"start_row": 7, "end_row": 7, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [{"bbox": null, "direction": null, "text": "FINISHED GARMENT MEASUREMENTS\nBack length fiom normal neckline\nJacket 15<sup>3</sup>/<sub>4</sub> 16 16<sup>1</sup>/<sub>4</sub> 16<sup>1</sup>/<sub>2 </sub>16<sup>3</sup>/<sub>4</sub> 17 Ins.\nSide length fiom natural waistline \nPants 39<sup>1</sup>/<sub>4</sub> 39<sup>1</sup>/<sub>2</sub> 39<sup>3</sup>/<sub>4 </sub>40 40<sup>1</sup>/<sub>4</sub> \" 40<sup>1</sup>/<sub>2</sub>\nWidth,each leg \nPants 11<sup>1</sup>/<sub>2</sub> 12 12<sup>1</sup>/<sub>2</sub> 13<sup>1</sup>/<sub>4</sub> 14<sup>1</sup>/<sub>4</sub> 15<sup>1</sup>/<sub>2</sub> \"", "score": null}], "bbox": {"p1": [295.97784423828125, 602.8757019042969], "p2": [1344.9966202221588, 613.7772827148438], "p3": [1342.9508962742411, 844.5722045898438], "p4": [293.47930908203125, 833.2063598632812]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": false, "content": [{"bbox": null, "direction": null, "text": "BODY MEASUREMENTS\nSize 6 8 10 12 14 16\nBust 30<sup>1</sup>/<sub>2</sub>  31<sup>1</sup>/<sub>2 </sub>31<sup>1</sup>/<sub>2</sub> 34 36 Ins\nWaist 23 24 25 26<sup>1</sup>/<sub>2 </sub>28 30\nHip 32<sup>1</sup>/<sub>2</sub> 33<sup>1</sup>/<sub>2 </sub>34<sup>1</sup>/<sub>2 </sub>36 38 40\nBack waist length  15<sup>1</sup>/<sub>2</sub> 15/<sub>4</sub> 16 16<sup>1</sup>/<sub>4</sub> 16<sup>1</sup>/<sub>2</sub> \" 16<sup>3</sup>/<sub>4</sub>", "score": null}], "bbox": {"p1": [316.13641357421875, 154.10560607910156], "p2": [1344.9966202221588, 172.44229125976562], "p3": [1342.9508962742411, 354.96113472285197], "p4": [312.66229248046875, 335.33323669433594]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": false, "content": [{"bbox": null, "direction": null, "text": "SUGGESTED FABRICS: All Garments - Soft Cotton and Cotton Blends ·\n<PERSON>llis · Chambray · Batik · Gauze · Lightweight Wool Crepe · Stable\nMetallic Knits; Jacket - also Cotton Knits · Jersey · Velvet. NOTE: All\nGarments - Not Suitable For Diagonals.", "score": null}], "bbox": {"p1": [287.4928894042969, 945.1408890284849], "p2": [1338.4774028676743, 945.140889028485], "p3": [1338.4774028676743, 1086.3411865234375], "p4": [287.6943359375, 1086.3411865234375]}, "lloc": {"start_row": 8, "end_row": 8, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": true, "content": [{"bbox": null, "direction": null, "text": "MISSES' UNLINED BOLERO JACKET AND PANTS:Unlined bolero jachet\nhas long dolman sleeves and optional shoulder pads;very lose-fitting pull-\non pants have fold back waistline casing with elastic,side seam pockets,low\ncrotch and tapered legs", "score": null}], "bbox": {"p1": [324.60858154296875, 22.225975036621094], "p2": [1338.4774028676743, 45.78038024902344], "p3": [1334.3129541592339, 178.91337741641533], "p4": [321.51287841796875, 154.10560607910156]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": true, "content": [{"bbox": null, "direction": null, "text": "VESTE BOLERO N###\nveste boléro non d\nfacultatives;pantaior\ndans les coutures de", "score": null}], "bbox": {"p1": [1334.3129541592339, 51.06299212598424], "p2": [1599.0, 65.0], "p3": [1600.0, 186.21259842519703], "p4": [1334.3129541592339, 178.91337741641533]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 9, "header": false, "content": [{"bbox": null, "direction": null, "text": "*With Nap ***With or Without Nap-Use With Nap Yardages and layouts\nfor pile or one-way desihn fabrics.Additional Fabric may be needed to match\nstripes or plaids.", "score": null}], "bbox": {"p1": [290.92889404296875, 833.2063598632812], "p2": [1342.9508962742411, 840.2378749763393], "p3": [1342.9508962742411, 953.0813598632812], "p4": [290.17352294921875, 945.1408890284849]}, "lloc": {"start_row": 7, "end_row": 7, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 10, "header": false, "content": [{"bbox": null, "direction": null, "text": "Pantalon\n115cm***\n150cm*", "score": null}], "bbox": {"p1": [1342.9508962742411, 526.1099987495236], "p2": [1598.8130081300824, 529.0], "p3": [1599.5952380952367, 620.4537179129462], "p4": [1344.9966202221588, 616.8144329896904]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 11, "header": false, "content": [{"bbox": null, "direction": null, "text": "MESURES DU VE###\nLongueur dos depu###\nVeste ###\nLongueur côté dep###\nPantalon 1###\nLargeur de jambe\nPantalon ###", "score": null}], "bbox": {"p1": [1344.9966202221588, 616.8144329896904], "p2": [1599.5952380952367, 620.4537179129462], "p3": [1599.934579439252, 837.1962616822439], "p4": [1342.9508962742411, 840.2378749763393]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 12, "header": false, "content": [{"bbox": null, "direction": null, "text": "Veste\n115cm***\n150cm*", "score": null}], "bbox": {"p1": [1344.9966202221588, 428.46948142878256], "p2": [1600.0, 435.0325203252032], "p3": [1598.8130081300824, 529.0], "p4": [1342.9508962742411, 526.1099987495236]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 13, "header": false, "content": [{"bbox": null, "direction": null, "text": "Pants\n44/45\"*** 2<sup>5</sup>/<sub>8</sub> 2<sup>5</sup>/<sub>8 </sub>2<sup>5</sup>/<sub>8</sub> 2<sup>5</sup>/<sub>8</sub> 2<sup>5</sup>/<sub>8 </sub>2<sup>3</sup>/<sub>4</sub> Yds.\n58/60\"* 2 2 2 2 2<sup>1</sup>/<sub>8</sub>  2<sup>1</sup>/<sub>4 </sub>", "score": null}], "bbox": {"p1": [301.33856201171875, 509.4250183105469], "p2": [1344.9966202221588, 526.1099987495236], "p3": [1344.9966202221588, 620.4537179129462], "p4": [299.83380126953125, 602.8757019042969]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 14, "header": false, "content": [{"bbox": null, "direction": null, "text": "Jacket\n44/45\"*** 2<sup>1</sup>/<sub>4</sub> 2<sup>1</sup>/<sub>4 </sub>2<sup>###</sup>/<sub>8 </sub>2<sup>1</sup>/<sub>2 </sub>2<sup>5</sup>/<sub>8</sub> 2<sup>5</sup>/<sub>8 </sub>Yds.\n58/60\"* 1 1/<sub>4</sub> 1<sup>7</sup>/<sub>8</sub> 1<sup>7</sup>/<sub>8</sub> 2 \" 2<sup>1</sup>/<sub>8</sub>", "score": null}], "bbox": {"p1": [305.66143798828125, 409.28553771972656], "p2": [1344.9966202221588, 425.2398681640625], "p3": [1342.9508962742411, 526.1099987495236], "p4": [304.04571533203125, 509.4250183105469]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 15, "header": false, "content": [{"bbox": null, "direction": null, "text": "NOTIONS: Thread; Jacket -<sup>1</sup>/<sub>4</sub>\" Thick Covered \"Raglan\"  Style Shoulder\nPads - Use McCall's #5212 (Opt.); Pants -1 Yd. of 1<sup>1</sup>/<sub>4</sub>\" Wide Elastic.", "score": null}], "bbox": {"p1": [282.015380859375, 1086.3411865234375], "p2": [1338.4774028676743, 1076.9867347565712], "p3": [1338.4774028676743, 1190.1485595703125], "p4": [283.071533203125, 1200.0001220703125]}, "lloc": {"start_row": 9, "end_row": 9, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 16, "header": false, "content": [{"bbox": null, "direction": null, "text": "TAILLES", "score": null}], "bbox": {"p1": [1344.9966202221588, 392.63419035663765], "p2": [1600.0, 399.7874015748039], "p3": [1600.0, 435.0325203252032], "p4": [1344.9966202221588, 428.46948142878256]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 17, "header": false, "content": [{"bbox": null, "direction": null, "text": "Séries:", "score": null}], "bbox": {"p1": [1342.9508962742411, 354.96113472285197], "p2": [1599.7874015748039, 364.20638209697427], "p3": [1600.0, 399.7874015748039], "p4": [1344.9966202221588, 392.63419035663765]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 18, "header": false, "content": [{"bbox": null, "direction": null, "text": "SIZES 6 8 10 12 14 16", "score": null}], "bbox": {"p1": [308.36334228515625, 373.29364013671875], "p2": [1344.9966202221588, 392.63419035663765], "p3": [1344.9966202221588, 428.46948142878256], "p4": [307.709228515625, 409.28553771972656]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 19, "header": false, "content": [{"bbox": null, "direction": null, "text": "Combinations: A(6-8-10), B(12-14-16)", "score": null}], "bbox": {"p1": [309.0714111328125, 335.33323669433594], "p2": [1344.9966202221588, 354.96113472285197], "p3": [1344.9966202221588, 392.63419035663765], "p4": [308.36334228515625, 373.29364013671875]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}