{"table_ind": "ia_100000001875_7", "image_path": "ia_100000001875_7.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": false, "content": [{"bbox": null, "direction": null, "text": "Given a pair of lines cut by a transversal, identify\nCorresponding, Alternate Interior, Alternate Fxterrior, Same\nSide Interior, Same Side Exterior angles. Solve problems\nusing these ideas.", "score": null}], "bbox": {"p1": [107.6966559488861, 139.00676350872513], "p2": [363.2814160730084, 124.91566539620806], "p3": [366.8072174738838, 177.34103365569163], "p4": [111.5, 189.90476190476207]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "Prove four Parallel Line/Transversal Theorems (AIA, AEA,\nSSI, SSE).", "score": null}], "bbox": {"p1": [113.875, 254.1709579180509], "p2": [369.0493343415595, 243.46148876962866], "p3": [369.0493343415595, 270.8532266613661], "p4": [113.875, 279.1395348837209]}, "lloc": {"start_row": 10, "end_row": 10, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": false, "content": [{"bbox": null, "direction": null, "text": "Construct parallel lines using AIA, Corresponding,\nPerpendicular to Perpendicular (also two lines parallel to a\nthird are parallel).", "score": null}], "bbox": {"p1": [118.65421134197149, 367.9047553652809], "p2": [371.5720400746407, 360.41208936903234], "p3": [374.14759642890743, 396.6565161897926], "p4": [118.65421134197149, 402.6679076278062]}, "lloc": {"start_row": 15, "end_row": 15, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "Use complemenfary, supplementary and congruent to\ncompare two angles.", "score": null}], "bbox": {"p1": [103.61726831498319, 43.79753019285696], "p2": [361.633648542077, 25.34463247326414], "p3": [363.2814160730084, 52.05003697919733], "p4": [105.40132522583008, 70.52803039550781]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [{"bbox": null, "direction": null, "text": "Solve problems using Parallel Line/Transversal Theorems\n(AIA, AEA, SSI, SSE).", "score": null}], "bbox": {"p1": [111.5, 228.85714285714297], "p2": [366.8072174738838, 217.7150997150993], "p3": [369.0493343415595, 243.46148876962866], "p4": [113.875, 254.1709579180509]}, "lloc": {"start_row": 9, "end_row": 9, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": false, "content": [{"bbox": null, "direction": null, "text": "Given two intersecting lines, identify Linear Pairs and\nVertical Angles. Use Theorems of these angle pairs to solve\nproblems.", "score": null}], "bbox": {"p1": [103.61726831498319, 68.17309301503192], "p2": [361.633648542077, 52.05003697919733], "p3": [363.2814160730084, 94.22753287520982], "p4": [107.6966559488861, 110.42788314819336]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": false, "content": [{"bbox": null, "direction": null, "text": "Use the Converse of Parailllel Line/Transversal Theorems to\nsolve problems.", "score": null}], "bbox": {"p1": [113.875, 279.1395348837209], "p2": [369.0493343415595, 270.8532266613661], "p3": [369.0493343415595, 295.17150997151003], "p4": [116.06053867237824, 304.42832080200543]}, "lloc": {"start_row": 11, "end_row": 11, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": false, "content": [{"bbox": null, "direction": null, "text": "Prove the converse of Parallel Line/Transversal Theorems\n(AIA,AEA,SSI,SSE).", "score": null}], "bbox": {"p1": [116.06053867237824, 328.0511177512173], "p2": [371.5720400746407, 319.2247767008086], "p3": [371.5720400746407, 345.2421652421655], "p4": [118.65421134197149, 353.53809600024374]}, "lloc": {"start_row": 13, "end_row": 13, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": false, "content": [{"bbox": null, "direction": null, "text": "Solve peoblems you b###n't seen before, u#ing a##dy#is and synthesis of the\ninfoemation ##arned so far. Prepare for Unit Test; Look over Skills List to Self Assess\nPrep##dness. Review homework for qucstions. Do arry Review Activities with an eye\n#ow#ds identifying gaps. If there are any gap#, go to the Optienal I##t####i###l\nResources on Moodle, and try to leam so fill these pap#", "score": null}], "bbox": {"p1": [121.22226236286988, 478.1240712483684], "p2": [379.06322989257563, 474.215087890625], "p3": [379.06322989257563, 524.5513667059295], "p4": [121.2222623628699, 528.2057006835937]}, "lloc": {"start_row": 21, "end_row": 21, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 9, "header": false, "content": [{"bbox": null, "direction": null, "text": "Define parallel lines (translation) and perpendicular lines\n(rotation of 90 degrees) in terms of rigid transformations.", "score": null}], "bbox": {"p1": [111.5, 189.90476190476207], "p2": [366.8072174738838, 177.34103365569163], "p3": [366.8072174738838, 201.35612535612518], "p4": [111.5, 213.3263561834991]}, "lloc": {"start_row": 7, "end_row": 7, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 10, "header": false, "content": [{"bbox": null, "direction": null, "text": "Day 26\nMon oct26", "score": null}], "bbox": {"p1": [17.0, 257.0], "p2": [63.05844155844154, 254.1709579180509], "p3": [65.9540326340325, 281.713397068048], "p4": [18.141969141969184, 281.713397068048]}, "lloc": {"start_row": 10, "end_row": 10, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 11, "header": false, "content": [{"bbox": null, "direction": null, "text": "PIL06a", "score": null}], "bbox": {"p1": [63.05844155844154, 228.85714285714297], "p2": [111.5, 228.85714285714297], "p3": [113.875, 254.1709579180509], "p4": [63.05844155844154, 254.1709579180509]}, "lloc": {"start_row": 9, "end_row": 9, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 12, "header": false, "content": [{"bbox": null, "direction": null, "text": "Prove Vert<PERSON>.", "score": null}], "bbox": {"p1": [107.6966559488861, 123.37725537583165], "p2": [363.2814160730084, 107.8943375408279], "p3": [363.2814160730084, 124.91566539620806], "p4": [107.6966559488861, 139.00676350872513]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 13, "header": false, "content": [{"bbox": null, "direction": null, "text": "PIL07", "score": null}], "bbox": {"p1": [65.9540326340325, 328.0511177512173], "p2": [116.06053867237824, 328.0511177512173], "p3": [118.65421134197149, 353.53809600024374], "p4": [68.67001758022876, 353.53809600024374]}, "lloc": {"start_row": 13, "end_row": 13, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 14, "header": false, "content": [], "bbox": {"p1": [361.633648542077, 25.34463247326414], "p2": [396.89160222493575, 22.99715099715104], "p3": [396.89160222493575, 50.09768009768004], "p4": [361.633648542077, 52.05003697919733]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 15, "header": false, "content": [{"bbox": null, "direction": null, "text": "Construct a right angle.", "score": null}], "bbox": {"p1": [118.65421134197149, 353.53809600024374], "p2": [371.5720400746407, 345.2421652421655], "p3": [371.5720400746407, 360.41208936903234], "p4": [118.65421134197149, 367.9047553652809]}, "lloc": {"start_row": 14, "end_row": 14, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 16, "header": false, "content": [{"bbox": null, "direction": null, "text": "Establish the Corresponding <PERSON><PERSON> postulate.", "score": null}], "bbox": {"p1": [111.5, 213.3263561834991], "p2": [366.8072174738838, 201.35612535612518], "p3": [366.8072174738838, 217.7150997150993], "p4": [111.5, 228.85714285714297]}, "lloc": {"start_row": 8, "end_row": 8, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 17, "header": false, "content": [{"bbox": null, "direction": null, "text": "PIL06b", "score": null}], "bbox": {"p1": [63.05844155844154, 254.1709579180509], "p2": [113.875, 252.95431893687692], "p3": [113.875, 279.1395348837209], "p4": [65.9540326340325, 281.713397068048]}, "lloc": {"start_row": 10, "end_row": 10, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 18, "header": false, "content": [{"bbox": null, "direction": null, "text": "PIL 05b", "score": null}], "bbox": {"p1": [63.05844155844154, 213.3263561834991], "p2": [111.5, 213.3263561834991], "p3": [111.5, 228.85714285714297], "p4": [63.05844155844154, 228.85714285714297]}, "lloc": {"start_row": 8, "end_row": 8, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 19, "header": false, "content": [{"bbox": null, "direction": null, "text": "PIL 05a", "score": null}], "bbox": {"p1": [61.0, 189.90476190476207], "p2": [111.5, 189.90476190476207], "p3": [111.5, 213.3263561834991], "p4": [63.05844155844154, 216.1988603988604]}, "lloc": {"start_row": 7, "end_row": 7, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 20, "header": false, "content": [], "bbox": {"p1": [369.0493343415595, 270.8532266613661], "p2": [404.14434947768285, 270.8532266613661], "p3": [404.14434947768285, 295.17150997151003], "p4": [369.0493343415595, 295.17150997151003]}, "lloc": {"start_row": 11, "end_row": 11, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 21, "header": false, "content": [{"bbox": null, "direction": null, "text": "PIL01", "score": null}], "bbox": {"p1": [54.08381701305381, 43.79753019285696], "p2": [103.61726831498319, 43.79753019285696], "p3": [105.40132522583008, 68.17309301503192], "p4": [54.08381701305381, 68.17309301503192]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 22, "header": false, "content": [{"bbox": null, "direction": null, "text": "PIL02", "score": null}], "bbox": {"p1": [54.08381701305381, 68.17309301503192], "p2": [105.40132522583008, 68.17309301503192], "p3": [107.6966559488861, 107.8943375408279], "p4": [58.28092236762216, 107.8943375408279]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 23, "header": false, "content": [], "bbox": {"p1": [369.0493343415595, 243.46148876962866], "p2": [404.14434947768285, 243.46148876962866], "p3": [404.14434947768285, 270.8532266613661], "p4": [369.0493343415595, 270.8532266613661]}, "lloc": {"start_row": 10, "end_row": 10, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 24, "header": false, "content": [{"bbox": null, "direction": null, "text": "Construct perpendicular lines through a point off the line.", "score": null}], "bbox": {"p1": [118.65421134197149, 402.6679076278062], "p2": [374.14759642890743, 396.6565161897926], "p3": [374.14759642890743, 412.9072671134928], "p4": [118.65421134197149, 417.3641205921956]}, "lloc": {"start_row": 16, "end_row": 16, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 25, "header": false, "content": [{"bbox": null, "direction": null, "text": "SKILL", "score": null}], "bbox": {"p1": [54.08381701305381, 27.42921206039288], "p2": [103.61726831498319, 27.42921206039288], "p3": [103.61726831498319, 43.79753019285696], "p4": [54.08381701305381, 43.79753019285696]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 26, "header": false, "content": [{"bbox": null, "direction": null, "text": "PIL 07h", "score": null}], "bbox": {"p1": [65.9540326340325, 304.42832080200543], "p2": [116.06053867237824, 304.42832080200543], "p3": [116.06053867237824, 328.0511177512173], "p4": [65.9540326340325, 328.0511177512173]}, "lloc": {"start_row": 12, "end_row": 12, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 27, "header": false, "content": [{"bbox": null, "direction": null, "text": "PIL05", "score": null}], "bbox": {"p1": [58.28092236762216, 139.00676350872513], "p2": [107.6966559488861, 139.00676350872513], "p3": [111.5, 189.90476190476207], "p4": [61.0, 189.90476190476207]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 28, "header": false, "content": [{"bbox": null, "direction": null, "text": "Day 24\nTues Oct20\nDay 24\nTues Oct20\nDay 24\nTues Oct20", "score": null}], "bbox": {"p1": [8.301117999846156, 43.79753019285696], "p2": [54.08381701305381, 41.63225555419922], "p3": [58.28092236762216, 139.00676350872513], "p4": [12.945812225341797, 141.45068359375]}, "lloc": {"start_row": 2, "end_row": 5, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 29, "header": false, "content": [{"bbox": null, "direction": null, "text": "Construct perpendicular lines through a point on the line.", "score": null}], "bbox": {"p1": [121.2222623628699, 417.3641205921956], "p2": [374.14759642890743, 412.9072671134928], "p3": [374.14759642890743, 429.2215938151505], "p4": [121.2222623628699, 433.8912656878122]}, "lloc": {"start_row": 17, "end_row": 17, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 30, "header": false, "content": [], "bbox": {"p1": [18.141969141969184, 281.713397068048], "p2": [65.9540326340325, 281.713397068048], "p3": [68.67001758022876, 353.53809600024374], "p4": [21.364771965123964, 357.125]}, "lloc": {"start_row": 11, "end_row": 13, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 31, "header": false, "content": [{"bbox": null, "direction": null, "text": "PIL 07a", "score": null}], "bbox": {"p1": [65.9540326340325, 281.713397068048], "p2": [113.875, 281.713397068048], "p3": [116.06053867237824, 304.42832080200543], "p4": [65.9540326340325, 304.42832080200543]}, "lloc": {"start_row": 11, "end_row": 11, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 32, "header": false, "content": [{"bbox": null, "direction": null, "text": "Construct rectangle (not a square).", "score": null}], "bbox": {"p1": [121.22226236286988, 431.1783752441406], "p2": [376.5037516623821, 427.3818664550781], "p3": [376.5037516623821, 446.0192237093239], "p4": [121.22226236286988, 449.75039656532]}, "lloc": {"start_row": 18, "end_row": 18, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 33, "header": false, "content": [], "bbox": {"p1": [369.0493343415595, 295.17150997151003], "p2": [404.14434947768285, 295.17150997151003], "p3": [406.5707502374169, 319.2247767008086], "p4": [371.5720400746407, 319.2247767008086]}, "lloc": {"start_row": 12, "end_row": 12, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 34, "header": false, "content": [{"bbox": null, "direction": null, "text": "Day 28\nThurs Oct29\nDay 28\nThurs Oct29\nDay 28\nThurs Oct29\nDay 28\nThurs Oct29\nDay 28\nThurs Oct29\nDay 28\nThurs Oct29\nDay 28\nThurs Oct29", "score": null}], "bbox": {"p1": [21.364771965123964, 357.125], "p2": [68.67001758022876, 353.53809600024374], "p3": [71.3085902620262, 485.81662954714193], "p4": [23.262063845582816, 490.0]}, "lloc": {"start_row": 14, "end_row": 20, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 35, "header": false, "content": [{"bbox": null, "direction": null, "text": "Day 25\nThurs Oct22\nDay 25\nThurs Oct22\nDay 25\nThurs Oct22\nDay 25\nThurs Oct22", "score": null}], "bbox": {"p1": [12.856527977044607, 139.00676350872513], "p2": [58.28092236762216, 139.00676350872513], "p3": [63.05844155844154, 257.0], "p4": [17.0, 257.0]}, "lloc": {"start_row": 6, "end_row": 9, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 36, "header": false, "content": [{"bbox": null, "direction": null, "text": "Day 29\nMon Nov2", "score": null}], "bbox": {"p1": [23.262063845582816, 490.0], "p2": [71.3085902620262, 485.81662954714193], "p3": [71.64071293435825, 533.2764976958524], "p4": [24.184331797235018, 537.7373271889401]}, "lloc": {"start_row": 21, "end_row": 21, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 37, "header": false, "content": [], "bbox": {"p1": [371.5720400746407, 319.2247767008086], "p2": [406.5707502374169, 319.2247767008086], "p3": [406.5707502374169, 345.2421652421655], "p4": [371.5720400746407, 345.2421652421655]}, "lloc": {"start_row": 13, "end_row": 13, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 38, "header": false, "content": [{"bbox": null, "direction": null, "text": "Prove <PERSON>ar <PERSON>.", "score": null}], "bbox": {"p1": [105.40132522583008, 107.8943375408279], "p2": [361.633648542077, 94.22753287520982], "p3": [363.2814160730084, 110.42788314819336], "p4": [107.6966559488861, 123.37725537583165]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 39, "header": false, "content": [{"bbox": null, "direction": null, "text": "PIL Unit Test.", "score": null}], "bbox": {"p1": [121.22226236286988, 524.5513667059295], "p2": [379.06322989257563, 521.1671142578125], "p3": [379.06322989257563, 541.0], "p4": [121.22226236286988, 544.009521484375]}, "lloc": {"start_row": 22, "end_row": 22, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 40, "header": false, "content": [], "bbox": {"p1": [396.89160222493575, 22.99715099715104], "p2": [434.4503029964226, 20.17038668714804], "p3": [434.8518518518522, 47.690679690679644], "p4": [396.89160222493575, 50.09768009768004]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 41, "header": false, "content": [], "bbox": {"p1": [371.5720400746407, 360.41208936903234], "p2": [406.5707502374169, 360.41208936903234], "p3": [409.7488129154795, 396.6565161897926], "p4": [374.14759642890743, 396.6565161897926]}, "lloc": {"start_row": 15, "end_row": 15, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 42, "header": false, "content": [], "bbox": {"p1": [366.8072174738838, 217.7150997150993], "p2": [401.6880773547438, 216.1988603988604], "p3": [404.14434947768285, 243.46148876962866], "p4": [369.0493343415595, 243.46148876962866]}, "lloc": {"start_row": 9, "end_row": 9, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 43, "header": false, "content": [], "bbox": {"p1": [361.633648542077, 52.05003697919733], "p2": [396.89160222493575, 50.09768009768004], "p3": [398.3645120311788, 92.13634513634497], "p4": [361.633648542077, 94.22753287520982]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 44, "header": false, "content": [{"bbox": null, "direction": null, "text": "Day", "score": null}], "bbox": {"p1": [7.546594113104675, 27.42921206039288], "p2": [54.08381701305381, 27.42921206039288], "p3": [54.08381701305381, 43.79753019285696], "p4": [8.301117999846156, 43.79753019285696]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 45, "header": false, "content": [{"bbox": null, "direction": null, "text": "Day 29\nMon Nov2", "score": null}], "bbox": {"p1": [71.3085902620262, 485.81662954714193], "p2": [121.2222623628699, 481.1810505822238], "p3": [121.2222623628699, 528.2057006835937], "p4": [71.64071293435825, 533.2764976958524]}, "lloc": {"start_row": 21, "end_row": 21, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 46, "header": false, "content": [{"bbox": null, "direction": null, "text": "Day 30\nTues Nov 3", "score": null}], "bbox": {"p1": [71.64071293435825, 533.2764976958524], "p2": [121.2222623628699, 528.2057006835937], "p3": [121.22226236286988, 545.0], "p4": [72.64516129032268, 550.5391705069123]}, "lloc": {"start_row": 22, "end_row": 22, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 47, "header": false, "content": [], "bbox": {"p1": [366.8072174738838, 177.34103365569163], "p2": [401.6880773547438, 174.87431518897293], "p3": [401.6880773547438, 201.35612535612518], "p4": [366.8072174738838, 201.35612535612518]}, "lloc": {"start_row": 7, "end_row": 7, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 48, "header": false, "content": [], "bbox": {"p1": [363.2814160730084, 124.91566539620806], "p2": [398.3645120311788, 123.37725537583165], "p3": [401.6880773547438, 174.87431518897293], "p4": [366.8072174738838, 177.34103365569163]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 49, "header": false, "content": [], "bbox": {"p1": [376.5037516623821, 446.0192237093239], "p2": [409.7488129154795, 446.0192237093239], "p3": [412.3621261750005, 461.95225589012335], "p4": [376.5037516623822, 461.95225589012335]}, "lloc": {"start_row": 19, "end_row": 19, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 50, "header": false, "content": [], "bbox": {"p1": [371.5720400746407, 345.2421652421655], "p2": [406.5707502374169, 345.2421652421655], "p3": [406.5707502374169, 360.41208936903234], "p4": [371.5720400746407, 360.41208936903234]}, "lloc": {"start_row": 14, "end_row": 14, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 51, "header": false, "content": [{"bbox": null, "direction": null, "text": "Construct a square.", "score": null}], "bbox": {"p1": [121.22226236286988, 449.75039656532], "p2": [376.5037516623821, 446.0192237093239], "p3": [376.5037516623822, 461.95225589012335], "p4": [121.22226236286988, 465.1022719520938]}, "lloc": {"start_row": 19, "end_row": 19, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 52, "header": false, "content": [], "bbox": {"p1": [366.8072174738838, 201.35612535612518], "p2": [401.6880773547438, 201.35612535612518], "p3": [401.6880773547438, 216.1988603988604], "p4": [366.8072174738838, 217.7150997150993]}, "lloc": {"start_row": 8, "end_row": 8, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 53, "header": false, "content": [{"bbox": null, "direction": null, "text": "DESCRIPTION", "score": null}], "bbox": {"p1": [103.61726831498319, 27.42921206039288], "p2": [361.633648542077, 9.841795205957169], "p3": [361.633648542077, 27.42921206039288], "p4": [103.61726831498319, 43.79753019285696]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 54, "header": false, "content": [{"bbox": null, "direction": null, "text": "56", "score": null}], "bbox": {"p1": [379.06322989257563, 478.1240712483684], "p2": [412.3621261750005, 478.0239189326605], "p3": [412.3621261750005, 524.5513667059295], "p4": [379.06322989257563, 524.5513667059295]}, "lloc": {"start_row": 21, "end_row": 21, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 55, "header": false, "content": [], "bbox": {"p1": [374.14759642890743, 396.6565161897926], "p2": [409.7488129154795, 396.6565161897926], "p3": [409.7488129154795, 412.9072671134928], "p4": [374.14759642890743, 412.9072671134928]}, "lloc": {"start_row": 16, "end_row": 16, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 56, "header": false, "content": [], "bbox": {"p1": [374.14759642890743, 429.2215938151505], "p2": [412.3621261750005, 429.2215938151505], "p3": [409.7488129154795, 446.0192237093239], "p4": [376.5037516623821, 446.0192237093239]}, "lloc": {"start_row": 18, "end_row": 18, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 57, "header": false, "content": [{"bbox": null, "direction": null, "text": "PIL52", "score": null}], "bbox": {"p1": [68.67001758022876, 402.6679076278062], "p2": [118.65421134197149, 402.6679076278062], "p3": [121.2222623628699, 417.3641205921956], "p4": [68.67001758022876, 420.5]}, "lloc": {"start_row": 16, "end_row": 16, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 58, "header": false, "content": [{"bbox": null, "direction": null, "text": "PIL50", "score": null}], "bbox": {"p1": [68.67001758022876, 353.53809600024374], "p2": [118.65421134197149, 353.53809600024374], "p3": [118.65421134197149, 367.9047553652809], "p4": [68.67001758022876, 367.9047553652809]}, "lloc": {"start_row": 14, "end_row": 14, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 59, "header": false, "content": [{"bbox": null, "direction": null, "text": "PIL03", "score": null}], "bbox": {"p1": [58.28092236762216, 107.8943375408279], "p2": [107.6966559488861, 107.8943375408279], "p3": [107.6966559488861, 123.37725537583165], "p4": [58.28092236762216, 123.37725537583165]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 60, "header": false, "content": [{"bbox": null, "direction": null, "text": "Day 30\nTues Nov 3", "score": null}], "bbox": {"p1": [24.184331797235018, 537.7373271889401], "p2": [71.64071293435825, 533.2764976958524], "p3": [71.37359176733412, 550.5391705069123], "p4": [24.573033707865306, 553.696629213483]}, "lloc": {"start_row": 22, "end_row": 22, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 61, "header": false, "content": [{"bbox": null, "direction": null, "text": "Start Unit Review - Unit Review Packet due on Monday", "score": null}], "bbox": {"p1": [121.22226236286988, 465.1022719520938], "p2": [379.06322989257563, 461.95225589012335], "p3": [379.06322989257563, 478.1240712483684], "p4": [121.2222623628699, 481.1810505822238]}, "lloc": {"start_row": 20, "end_row": 20, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 62, "header": false, "content": [{"bbox": null, "direction": null, "text": "PIL51", "score": null}], "bbox": {"p1": [68.67001758022876, 367.9047553652809], "p2": [118.65421134197149, 367.9047553652809], "p3": [118.65421134197149, 402.6679076278062], "p4": [68.67001758022876, 405.0]}, "lloc": {"start_row": 15, "end_row": 15, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 63, "header": false, "content": [{"bbox": null, "direction": null, "text": "PIL53", "score": null}], "bbox": {"p1": [68.67001758022876, 420.5], "p2": [121.2222623628699, 417.3641205921956], "p3": [121.2222623628699, 433.8912656878122], "p4": [68.67001758022876, 436.5]}, "lloc": {"start_row": 17, "end_row": 17, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 64, "header": false, "content": [{"bbox": null, "direction": null, "text": "66", "score": null}], "bbox": {"p1": [379.06322989257563, 524.5513667059295], "p2": [412.3621261750005, 524.5513667059295], "p3": [414.77445109780456, 541.7944111776451], "p4": [379.06322989257563, 541.0]}, "lloc": {"start_row": 22, "end_row": 22, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 65, "header": false, "content": [], "bbox": {"p1": [374.14759642890743, 412.9072671134928], "p2": [409.7488129154795, 412.9072671134928], "p3": [412.3621261750005, 429.2215938151505], "p4": [374.14759642890743, 429.2215938151505]}, "lloc": {"start_row": 17, "end_row": 17, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 66, "header": false, "content": [{"bbox": null, "direction": null, "text": "Review", "score": null}], "bbox": {"p1": [68.67001758022876, 469.0], "p2": [121.22226236286988, 465.1022719520938], "p3": [121.2222623628699, 481.1810505822238], "p4": [71.3085902620262, 485.81662954714193]}, "lloc": {"start_row": 20, "end_row": 20, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 67, "header": false, "content": [{"bbox": null, "direction": null, "text": "PIL54", "score": null}], "bbox": {"p1": [68.67001758022876, 436.5], "p2": [121.2222623628699, 433.8912656878122], "p3": [121.22226236286988, 449.75039656532], "p4": [68.67001758022876, 453.5]}, "lloc": {"start_row": 18, "end_row": 18, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 68, "header": false, "content": [], "bbox": {"p1": [363.2814160730084, 107.8943375408279], "p2": [398.3645120311788, 107.8943375408279], "p3": [398.3645120311788, 123.37725537583165], "p4": [363.2814160730084, 124.91566539620806]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 69, "header": false, "content": [{"bbox": null, "direction": null, "text": "PIL55", "score": null}], "bbox": {"p1": [68.67001758022876, 453.5], "p2": [121.22226236286988, 449.75039656532], "p3": [121.22226236286988, 465.1022719520938], "p4": [68.67001758022876, 471.0]}, "lloc": {"start_row": 19, "end_row": 19, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 70, "header": false, "content": [], "bbox": {"p1": [361.633648542077, 94.22753287520982], "p2": [398.3645120311788, 92.13634513634497], "p3": [398.3645120311788, 107.8943375408279], "p4": [363.2814160730084, 107.8943375408279]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 71, "header": false, "content": [], "bbox": {"p1": [406.5707502374169, 345.2421652421655], "p2": [435.0, 345.2421652421655], "p3": [435.28490028490023, 360.41208936903234], "p4": [406.5707502374169, 360.41208936903234]}, "lloc": {"start_row": 14, "end_row": 14, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 72, "header": false, "content": [], "bbox": {"p1": [409.7488129154795, 396.6565161897926], "p2": [435.28490028490023, 396.6565161897926], "p3": [435.0, 412.9072671134928], "p4": [409.7488129154795, 412.9072671134928]}, "lloc": {"start_row": 16, "end_row": 16, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 73, "header": false, "content": [], "bbox": {"p1": [404.14434947768285, 295.17150997151003], "p2": [435.0, 295.17150997151003], "p3": [435.1424501424499, 319.2247767008086], "p4": [406.5707502374169, 319.2247767008086]}, "lloc": {"start_row": 12, "end_row": 12, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 74, "header": false, "content": [], "bbox": {"p1": [406.5707502374169, 319.2247767008086], "p2": [435.1424501424499, 319.2247767008086], "p3": [435.0, 345.2421652421655], "p4": [406.5707502374169, 345.2421652421655]}, "lloc": {"start_row": 13, "end_row": 13, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 75, "header": false, "content": [], "bbox": {"p1": [412.3621261750005, 429.2215938151505], "p2": [435.0, 429.2215938151505], "p3": [434.9287749287748, 446.0192237093239], "p4": [409.7488129154795, 446.0192237093239]}, "lloc": {"start_row": 18, "end_row": 18, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 76, "header": false, "content": [], "bbox": {"p1": [406.5707502374169, 360.41208936903234], "p2": [435.28490028490023, 360.41208936903234], "p3": [435.28490028490023, 396.6565161897926], "p4": [409.7488129154795, 396.6565161897926]}, "lloc": {"start_row": 15, "end_row": 15, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 77, "header": false, "content": [], "bbox": {"p1": [409.7488129154795, 412.9072671134928], "p2": [435.0, 412.9072671134928], "p3": [435.0, 429.2215938151505], "p4": [412.3621261750005, 429.2215938151505]}, "lloc": {"start_row": 17, "end_row": 17, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 78, "header": false, "content": [], "bbox": {"p1": [409.7488129154795, 446.0192237093239], "p2": [434.9287749287748, 446.0192237093239], "p3": [435.85754985754966, 461.95225589012335], "p4": [412.3621261750005, 461.95225589012335]}, "lloc": {"start_row": 19, "end_row": 19, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 79, "header": false, "content": [], "bbox": {"p1": [404.14434947768285, 270.8532266613661], "p2": [435.0, 269.28490028490023], "p3": [435.0, 295.17150997151003], "p4": [404.14434947768285, 295.17150997151003]}, "lloc": {"start_row": 11, "end_row": 11, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 80, "header": false, "content": [], "bbox": {"p1": [404.14434947768285, 243.46148876962866], "p2": [435.8575498575501, 241.50142450142448], "p3": [435.0, 269.28490028490023], "p4": [404.14434947768285, 270.8532266613661]}, "lloc": {"start_row": 10, "end_row": 10, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 81, "header": false, "content": [], "bbox": {"p1": [401.6880773547438, 174.87431518897293], "p2": [436.0, 174.87431518897293], "p3": [436.0, 198.28490028490023], "p4": [401.6880773547438, 201.35612535612518]}, "lloc": {"start_row": 7, "end_row": 7, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 82, "header": false, "content": [], "bbox": {"p1": [398.3645120311788, 92.13634513634497], "p2": [436.0, 88.42979242979209], "p3": [436.0, 104.28774928774874], "p4": [398.3645120311788, 107.8943375408279]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 83, "header": false, "content": [], "bbox": {"p1": [398.3645120311788, 107.8943375408279], "p2": [436.0, 104.28774928774874], "p3": [436.0, 120.5902168768605], "p4": [398.3645120311788, 123.37725537583165]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 84, "header": false, "content": [], "bbox": {"p1": [401.6880773547438, 201.35612535612518], "p2": [436.0, 198.28490028490023], "p3": [436.0, 213.3263561834991], "p4": [401.6880773547438, 216.1988603988604]}, "lloc": {"start_row": 8, "end_row": 8, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 85, "header": false, "content": [], "bbox": {"p1": [401.6880773547438, 216.1988603988604], "p2": [436.0, 213.3263561834991], "p3": [435.8575498575501, 241.50142450142448], "p4": [404.14434947768285, 243.46148876962866]}, "lloc": {"start_row": 9, "end_row": 9, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 86, "header": false, "content": [], "bbox": {"p1": [376.5037516623822, 461.95225589012335], "p2": [412.3621261750005, 461.95225589012335], "p3": [412.3621261750005, 478.0239189326605], "p4": [379.06322989257563, 478.1240712483684]}, "lloc": {"start_row": 20, "end_row": 20, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 87, "header": false, "content": [], "bbox": {"p1": [412.3621261750005, 461.95225589012335], "p2": [435.85754985754966, 461.95225589012335], "p3": [436.0, 478.1461594381482], "p4": [412.3621261750005, 478.0239189326605]}, "lloc": {"start_row": 20, "end_row": 20, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 88, "header": false, "content": [{"bbox": null, "direction": null, "text": "PIL04", "score": null}], "bbox": {"p1": [58.28092236762216, 123.37725537583165], "p2": [107.6966559488861, 123.37725537583165], "p3": [107.6966559488861, 139.00676350872513], "p4": [58.28092236762216, 139.00676350872513]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 89, "header": false, "content": [], "bbox": {"p1": [398.3645120311788, 123.37725537583165], "p2": [436.0, 120.5902168768605], "p3": [436.0, 174.87431518897293], "p4": [401.6880773547438, 177.34103365569163]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 90, "header": true, "content": [{"bbox": null, "direction": null, "text": "Unit 4: PIL - Parallel and Intersecting Lines (using Two Column Proofs)", "score": null}], "bbox": {"p1": [433.69598388671875, -6.065579414367676], "p2": [434.5715026855469, 11.73571491241455], "p3": [7.78082275390625, 32.725894927978516], "p4": [6.9053168296813965, 14.924601554870605]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 91, "header": false, "content": [{"bbox": null, "direction": null, "text": "93", "score": null}], "bbox": {"p1": [412.3621261750005, 478.0239189326605], "p2": [436.0, 478.1461594381482], "p3": [436.0, 524.5513667059295], "p4": [412.3621261750005, 524.5513667059295]}, "lloc": {"start_row": 21, "end_row": 21, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 92, "header": false, "content": [{"bbox": null, "direction": null, "text": "94", "score": null}], "bbox": {"p1": [412.3621261750005, 524.5513667059295], "p2": [436.0, 524.5513667059295], "p3": [436.0, 542.3932135728546], "p4": [414.77445109780456, 541.7944111776451]}, "lloc": {"start_row": 22, "end_row": 22, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 93, "header": false, "content": [], "bbox": {"p1": [396.89160222493575, 50.09768009768004], "p2": [434.8518518518522, 47.690679690679644], "p3": [436.0, 88.42979242979209], "p4": [398.3645120311788, 92.13634513634497]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 94, "header": false, "content": [], "bbox": {"p1": [361.633648542077, 9.841795205957169], "p2": [434.0436057910674, 4.478404730592047], "p3": [434.4503029964226, 20.17038668714804], "p4": [361.633648542077, 25.34463247326414]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 3, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 95, "header": false, "content": [{"bbox": null, "direction": null, "text": "Prove the Triangle Angle Sum Theorem.", "score": null}], "bbox": {"p1": [116.06053867237824, 304.42832080200543], "p2": [369.0493343415595, 295.17150997151003], "p3": [371.5720400746407, 319.2247767008086], "p4": [116.06053867237824, 328.0511177512173]}, "lloc": {"start_row": 12, "end_row": 12, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}