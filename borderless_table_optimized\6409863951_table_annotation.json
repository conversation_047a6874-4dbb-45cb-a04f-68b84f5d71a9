{"table_ind": "6409863951", "image_path": "6409863951.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": false, "content": [{"bbox": null, "direction": null, "text": "Theminimum width of gates will be governed by the dass of roads on which the leved\ncrossing is situated and will be as under——\nAcriss Class I roads-F metres or X+2.5M.whichever is more.\nAcniss Class Il roads-1.5 metres or X+2 M whichever is more.\nAcrss Class Ill roads-5.0 metres or X+ 1.25 M. whichever is nore.\nAcriss Class IV roads -suitable width subject to 2 M. being the ninimum.", "score": null}], "bbox": {"p1": [161.48367032666226, 212.7296449270699], "p2": [510.3803661779388, 212.7296449270699], "p3": [510.3803661779388, 294.6894192780516], "p4": [161.48367032666226, 294.6894192780516]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 2, "end_col": 5}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "3", "score": null}], "bbox": {"p1": [161.48367032666226, 188.67799194031159], "p2": [248.9311722815322, 188.67799194031159], "p3": [248.9311722815322, 212.7296449270699], "p4": [161.48367032666226, 212.7296449270699]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": true, "content": [{"bbox": null, "direction": null, "text": "Remarks", "score": null}], "bbox": {"p1": [595.1514779744855, 145.45388511576266], "p2": [810.3274336283184, 146.22123893805292], "p3": [810.0526315789475, 188.67799194031159], "p4": [595.1514779744855, 188.67799194031159]}, "lloc": {"start_row": 0, "end_row": 1, "start_col": 7, "end_col": 7}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "'A' Class", "score": null}], "bbox": {"p1": [248.9311722815322, 165.33157721483678], "p2": [336.4170660468867, 165.33157721483678], "p3": [336.4170660468867, 188.67799194031159], "p4": [248.9311722815322, 188.67799194031159]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [{"bbox": null, "direction": null, "text": "'B' class", "score": null}], "bbox": {"p1": [336.4170660468867, 165.33157721483678], "p2": [424.0035981460422, 165.33157721483678], "p3": [424.0035981460422, 188.67799194031159], "p4": [336.4170660468867, 188.67799194031159]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": false, "content": [{"bbox": null, "direction": null, "text": "5", "score": null}], "bbox": {"p1": [336.4170660468867, 188.67799194031159], "p2": [424.0035981460422, 188.67799194031159], "p3": [424.0035981460422, 212.7296449270699], "p4": [336.4170660468867, 212.7296449270699]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": false, "content": [{"bbox": null, "direction": null, "text": "'C' class", "score": null}], "bbox": {"p1": [424.0035981460422, 165.33157721483678], "p2": [510.3803661779388, 165.33157721483678], "p3": [510.3803661779388, 188.67799194031159], "p4": [424.0035981460422, 188.67799194031159]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 5, "end_col": 5}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": false, "content": [{"bbox": null, "direction": null, "text": "'D' class", "score": null}], "bbox": {"p1": [510.3803661779388, 165.33157721483678], "p2": [595.1514779744855, 165.33157721483678], "p3": [595.1514779744855, 188.67799194031159], "p4": [510.3803661779388, 188.67799194031159]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 6, "end_col": 6}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": false, "content": [{"bbox": null, "direction": null, "text": "Special", "score": null}], "bbox": {"p1": [161.48367032666226, 165.33157721483678], "p2": [248.9311722815322, 165.33157721483678], "p3": [248.9311722815322, 188.67799194031159], "p4": [161.48367032666226, 188.67799194031159]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 9, "header": true, "content": [{"bbox": null, "direction": null, "text": "Details", "score": null}], "bbox": {"p1": [22.474032562262163, 145.54060899773867], "p2": [161.48367032666226, 145.43899277182823], "p3": [161.48367032666226, 188.67799194031159], "p4": [22.474032562262163, 188.67799194031159]}, "lloc": {"start_row": 0, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 10, "header": false, "content": [{"bbox": null, "direction": null, "text": "4", "score": null}], "bbox": {"p1": [248.9311722815322, 188.67799194031159], "p2": [336.4170660468867, 188.67799194031159], "p3": [336.4170660468867, 212.7296449270699], "p4": [248.9311722815322, 212.7296449270699]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 11, "header": false, "content": [{"bbox": null, "direction": null, "text": "7", "score": null}], "bbox": {"p1": [510.3803661779388, 188.67799194031159], "p2": [595.1514779744855, 188.67799194031159], "p3": [595.1514779744855, 212.7296449270699], "p4": [510.3803661779388, 212.7296449270699]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 6, "end_col": 6}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 12, "header": false, "content": [{"bbox": null, "direction": null, "text": "6", "score": null}], "bbox": {"p1": [424.0035981460422, 188.67799194031159], "p2": [510.3803661779388, 188.67799194031159], "p3": [510.3803661779388, 212.7296449270699], "p4": [424.0035981460422, 212.7296449270699]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 5, "end_col": 5}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 13, "header": false, "content": [{"bbox": null, "direction": null, "text": "5.5 metres (18')..\n2 metres more than vidth of gate.\nNot less than 45#\ntetween centre line\nof road and Raitway,", "score": null}], "bbox": {"p1": [336.4170660468867, 294.6894192780516], "p2": [424.0035981460422, 294.6894192780516], "p3": [424.0035981460422, 564.5128205128203], "p4": [336.4170660468867, 564.0]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 14, "header": false, "content": [{"bbox": null, "direction": null, "text": "15", "score": null}], "bbox": {"p1": [510.3803661779388, 212.7296449270699], "p2": [595.1514779744855, 212.7296449270699], "p3": [595.1514779744855, 294.6894192780516], "p4": [510.3803661779388, 294.6894192780516]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 6, "end_col": 6}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 15, "header": false, "content": [{"bbox": null, "direction": null, "text": "Minimum width of gates at\nright angles to the certre line\no the road.\nCld Standards:-\nNinimum length of guard rail\n(br a square crossing)\nAngle of crossing betveen gates.", "score": null}], "bbox": {"p1": [22.474032562262163, 212.7296449270699], "p2": [161.48367032666226, 212.7296449270699], "p3": [161.48367032666226, 564.5780334472656], "p4": [22.474032562262163, 564.1226677413094]}, "lloc": {"start_row": 3, "end_row": 4, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 16, "header": false, "content": [{"bbox": null, "direction": null, "text": "1.5 metres (18')..\n2 metres more than vidth of gate.\nNot less than 45#\ntetween centre line\nof road and Raitway,", "score": null}], "bbox": {"p1": [248.9311722815322, 294.6894192780516], "p2": [336.4170660468867, 294.6894192780516], "p3": [336.4170660468867, 564.0], "p4": [248.9311722815322, 565.0]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 17, "header": false, "content": [{"bbox": null, "direction": null, "text": "Minimum width of gates at\nright angles to the certre line\no the road.\nCid Standards:-\nNinimum length of guard rail\n(br a square crossing)\nNotless than 45#\nbetveen oentre line\nof rad and Railway.", "score": null}], "bbox": {"p1": [161.48367032666226, 294.6894192780516], "p2": [248.9311722815322, 294.6894192780516], "p3": [248.9311722815322, 565.0], "p4": [161.48367032666226, 564.5780334472656]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 18, "header": false, "content": [{"bbox": null, "direction": null, "text": "3.0m. (9) it un\nmanned\n5.5m (18)#\nmanned.\n2 metres morethan\nwidth of gate.\nNot less than 45#\nbetween centre line\nof road and Railway,", "score": null}], "bbox": {"p1": [424.0035981460422, 294.6894192780516], "p2": [510.3803661779388, 294.6894192780516], "p3": [510.3803661779388, 565.0], "p4": [424.0035981460422, 564.5128205128203]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 5, "end_col": 5}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 19, "header": false, "content": [{"bbox": null, "direction": null, "text": "2", "score": null}], "bbox": {"p1": [22.474032562262163, 188.67799194031159], "p2": [161.48367032666226, 188.67799194031159], "p3": [161.48367032666226, 212.7296449270699], "p4": [22.474032562262163, 212.7296449270699]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 20, "header": false, "content": [{"bbox": null, "direction": null, "text": "2.0m. (6)\nNot to be povided\nAt right ange to\nthe centre Ine of the Ralway", "score": null}], "bbox": {"p1": [510.3803661779388, 294.6894192780516], "p2": [595.1514779744855, 294.6894192780516], "p3": [595.1514779744855, 564.0], "p4": [510.3803661779388, 565.0]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 6, "end_col": 6}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 21, "header": false, "content": [{"bbox": null, "direction": null, "text": "8", "score": null}], "bbox": {"p1": [595.1514779744855, 188.67799194031159], "p2": [810.0526315789475, 188.67799194031159], "p3": [809.3651877133107, 212.7296449270699], "p4": [595.1514779744855, 212.7296449270699]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 7, "end_col": 7}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 22, "header": false, "content": [{"bbox": null, "direction": null, "text": "Where &gt; width of Carriage way. Gates shall be\nprovidec at special 'A' and '' class crossings.\nGates or chains shall be prorided at C' and 'D' cass\nlevel crossings only in the folowing cases-\n(Ⅰ) Whenthe line is on curve and the road and rail\nview is rot clear.\n(Ⅱ) Wher speed of trains andvolume of traffic are great.\n(Ⅲ)when the level crossing is within those limits at\na stattior between which shunting is normally\nlikaly to e carried out.\n(iv) On portions of the line wnich ara fenced through\nout on ole or both sides, except when efficient\ncattle guards are provided.\nIn the case of skew crossingthe length of the guard\nrail musi be increased in acordance with the\nformula =X=L/sin A where\nX required length,\nL= Minimum length measured\nat ight angles to the cantre line of the road,\nA= Argle between the certre line of the\nRcad and Failway.", "score": null}], "bbox": {"p1": [595.1514779744855, 212.7296449270699], "p2": [809.3651877133107, 212.7296449270699], "p3": [810.1331592689294, 564.2610966057441], "p4": [595.1514779744855, 564.0]}, "lloc": {"start_row": 3, "end_row": 4, "start_col": 7, "end_col": 7}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 23, "header": true, "content": [{"bbox": null, "direction": null, "text": "Dimetsions and details fer Various classes of crossings", "score": null}], "bbox": {"p1": [161.48367032666226, 145.43899277182823], "p2": [595.1514779744855, 145.45388511576266], "p3": [595.1514779744855, 165.33157721483678], "p4": [161.48367032666226, 165.33157721483678]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 2, "end_col": 6}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 24, "header": false, "content": [{"bbox": null, "direction": null, "text": "1", "score": null}], "bbox": {"p1": [0.2394701757320945, 188.67799194031159], "p2": [22.474032562262163, 188.67799194031159], "p3": [22.474032562262163, 212.7296449270699], "p4": [0.29761904761926417, 212.7296449270699]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 25, "header": true, "content": [{"bbox": null, "direction": null, "text": "<PERSON><PERSON>", "score": null}], "bbox": {"p1": [0.0, 145.82327449198192], "p2": [22.474032562262163, 145.54060899773867], "p3": [22.474032562262163, 188.67799194031159], "p4": [0.2394701757320945, 188.67799194031159]}, "lloc": {"start_row": 0, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 26, "header": false, "content": [{"bbox": null, "direction": null, "text": "1\n2\n3", "score": null}], "bbox": {"p1": [0.29761904761926417, 212.7296449270699], "p2": [22.474032562262163, 212.7296449270699], "p3": [22.474032562262163, 564.1226677413094], "p4": [0.0, 565.0]}, "lloc": {"start_row": 3, "end_row": 4, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}