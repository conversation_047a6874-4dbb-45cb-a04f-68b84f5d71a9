{"table_ind": "ia_100000002882_5", "image_path": "ia_100000002882_5.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": false, "content": [{"bbox": null, "direction": null, "text": "Always reads\n\"Loo easy\"\nbooks", "score": null}], "bbox": {"p1": [314.7777777777774, 662.3888888888887], "p2": [469.1666666666667, 657.7777777777777], "p3": [469.1666666666667, 740.5], "p4": [313.2341439135381, 745.0]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "Sticks with one\ngenre", "score": null}], "bbox": {"p1": [469.1666666666667, 657.7777777777777], "p2": [646.3703703703704, 657.7777777777777], "p3": [646.3703703703704, 738.0], "p4": [469.1666666666667, 740.5]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": true, "content": [{"bbox": null, "direction": null, "text": "Genres", "score": null}], "bbox": {"p1": [464.0, 217.34129692832767], "p2": [635.1814907698977, 216.0], "p3": [635.1814907698977, 255.9641638225255], "p4": [464.0, 255.9641638225255]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "Roads mostly\n\"too easy\"\nbooks", "score": null}], "bbox": {"p1": [313.2341439135381, 515.8221587689089], "p2": [466.8333333333333, 515.8221587689089], "p3": [469.1666666666667, 657.7777777777777], "p4": [314.7777777777774, 662.3888888888887]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [{"bbox": null, "direction": null, "text": "Reads books from\nthree or more\ngenres", "score": null}], "bbox": {"p1": [464.0, 255.9641638225255], "p2": [635.1814907698977, 255.9641638225255], "p3": [639.5537306959814, 392.0008116437803], "p4": [464.0, 394.45906432748535]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": false, "content": [{"bbox": null, "direction": null, "text": "Reads \"just\nright\" books\nand tries \"too\nhard\" boks\nsomesimes", "score": null}], "bbox": {"p1": [313.2341439135381, 251.98407281001133], "p2": [464.0, 255.9641638225255], "p3": [466.8333333333333, 392.0008116437803], "p4": [313.2341439135381, 394.45906432748535]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": false, "content": [{"bbox": null, "direction": null, "text": "Tries a different\ngenre once in\na while", "score": null}], "bbox": {"p1": [466.8333333333333, 515.8221587689089], "p2": [641.8089887640444, 512.9588014981273], "p3": [646.3703703703704, 657.7777777777777], "p4": [469.1666666666667, 657.7777777777777]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": true, "content": [{"bbox": null, "direction": null, "text": "Books Read", "score": null}], "bbox": {"p1": [172.72357530203698, 204.872340425532], "p2": [313.2341439135381, 212.0], "p3": [313.2341439135381, 251.98407281001133], "p4": [172.72357530203698, 245.9078014184397]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": false, "content": [{"bbox": null, "direction": null, "text": "Finishes 2\nbooks", "score": null}], "bbox": {"p1": [172.72357530203698, 515.8221587689089], "p2": [313.2341439135381, 515.8221587689089], "p3": [314.7777777777774, 662.3888888888887], "p4": [172.72357530203698, 665.4814814814814]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 9, "header": false, "content": [{"bbox": null, "direction": null, "text": "Reade mostly\n\"just right\" books", "score": null}], "bbox": {"p1": [313.2341439135381, 394.45906432748535], "p2": [464.0, 392.0008116437803], "p3": [466.8333333333333, 515.8221587689089], "p4": [313.2341439135381, 515.8221587689089]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 10, "header": true, "content": [{"bbox": null, "direction": null, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "score": null}], "bbox": {"p1": [313.2341439135381, 212.0], "p2": [464.0, 217.34129692832767], "p3": [464.0, 255.9641638225255], "p4": [313.2341439135381, 251.98407281001133]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 11, "header": false, "content": [{"bbox": null, "direction": null, "text": "Finishes 1\nbook", "score": null}], "bbox": {"p1": [172.72357530203698, 665.4814814814814], "p2": [314.7777777777774, 662.3888888888887], "p3": [313.2341439135381, 745.0], "p4": [172.72357530203698, 748.8888888888887]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 12, "header": false, "content": [{"bbox": null, "direction": null, "text": "Finishes 5 or\nmore books", "score": null}], "bbox": {"p1": [172.72357530203698, 245.9078014184397], "p2": [313.2341439135381, 251.98407281001133], "p3": [313.2341439135381, 394.45906432748535], "p4": [172.72357530203698, 392.0008116437803]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 13, "header": false, "content": [{"bbox": null, "direction": null, "text": "Reads books from\ntwo genres", "score": null}], "bbox": {"p1": [466.8333333333333, 394.45906432748535], "p2": [639.5537306959814, 392.0008116437803], "p3": [641.8089887640444, 512.9588014981273], "p4": [466.8333333333333, 515.8221587689089]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 14, "header": true, "content": [{"bbox": null, "direction": null, "text": "Interpretation", "score": null}], "bbox": {"p1": [635.1814907698977, 216.0], "p2": [812.0, 214.0], "p3": [812.2773145638785, 251.98407281001133], "p4": [635.1814907698977, 255.9641638225255]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 15, "header": false, "content": [{"bbox": null, "direction": null, "text": "Finishes 3 or\n4 books", "score": null}], "bbox": {"p1": [172.72357530203698, 392.0008116437803], "p2": [313.2341439135381, 394.45906432748535], "p3": [313.2341439135381, 515.8221587689089], "p4": [172.72357530203698, 515.8221587689089]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 16, "header": true, "content": [{"bbox": null, "direction": null, "text": "Level", "score": null}], "bbox": {"p1": [33.0, 204.0], "p2": [172.72357530203698, 204.872340425532], "p3": [172.72357530203698, 245.9078014184397], "p4": [30.93617021276623, 245.9078014184397]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 17, "header": false, "content": [{"bbox": null, "direction": null, "text": "Beginning", "score": null}], "bbox": {"p1": [26.33333333333303, 665.4814814814814], "p2": [172.72357530203698, 665.4814814814814], "p3": [172.72357530203698, 748.8888888888887], "p4": [27.0, 752.0]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 18, "header": false, "content": [{"bbox": null, "direction": null, "text": "Developing", "score": null}], "bbox": {"p1": [27.72474824534629, 515.8221587689089], "p2": [172.72357530203698, 515.8221587689089], "p3": [172.72357530203698, 665.4814814814814], "p4": [26.33333333333303, 665.4814814814814]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 19, "header": false, "content": [{"bbox": null, "direction": null, "text": "Offers incomplete\nor inaccurate\nresponse", "score": null}], "bbox": {"p1": [646.3703703703704, 657.7777777777777], "p2": [829.5, 657.7777777777777], "p3": [832.0, 738.0], "p4": [646.3703703703704, 738.0]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 20, "header": false, "content": [{"bbox": null, "direction": null, "text": "Provides literal\nInterpretation\nby summarizing\nevents and\nmaking personal\nconnections", "score": null}], "bbox": {"p1": [641.8089887640444, 512.9588014981273], "p2": [821.4382022471918, 512.9588014981273], "p3": [829.5, 657.7777777777777], "p4": [646.3703703703704, 657.7777777777777]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 21, "header": false, "content": [{"bbox": null, "direction": null, "text": "Shares accurate\ninterpretalion\nusing a summary,\ninferences, and\ns#cry structure", "score": null}], "bbox": {"p1": [639.5537306959814, 392.0008116437803], "p2": [817.3548356683568, 392.0008116437803], "p3": [821.4382022471918, 512.9588014981273], "p4": [641.8089887640444, 512.9588014981273]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 22, "header": false, "content": [{"bbox": null, "direction": null, "text": "<PERSON>es insightful\ninterpretation\nwith evidence\nfrom the book,\nauthor' s style,\nand genre", "score": null}], "bbox": {"p1": [635.1814907698977, 255.9641638225255], "p2": [812.2773145638785, 251.98407281001133], "p3": [817.3548356683568, 392.0008116437803], "p4": [639.5537306959814, 392.0008116437803]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 23, "header": false, "content": [{"bbox": null, "direction": null, "text": "Outatanding", "score": null}], "bbox": {"p1": [30.93617021276623, 245.9078014184397], "p2": [172.72357530203698, 245.9078014184397], "p3": [172.72357530203698, 392.0008116437803], "p4": [29.287100942091456, 392.0008116437803]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 24, "header": false, "content": [{"bbox": null, "direction": null, "text": "Proficient", "score": null}], "bbox": {"p1": [29.287100942091456, 392.0008116437803], "p2": [172.72357530203698, 392.0008116437803], "p3": [172.72357530203698, 515.8221587689089], "p4": [27.72474824534629, 515.8221587689089]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}