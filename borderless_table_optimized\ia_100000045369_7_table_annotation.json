{"table_ind": "ia_100000045369_7", "image_path": "ia_100000045369_7.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": false, "content": [{"bbox": null, "direction": null, "text": "(2+5)+3=2+(5+3)", "score": null}], "bbox": {"p1": [284.73041226193124, 135.8235294117644], "p2": [563.7607655502388, 131.0], "p3": [564.7177033492826, 170.47846889952189], "p4": [284.73041226193124, 174.22352941176467]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "4+6=6+4", "score": null}], "bbox": {"p1": [284.73041226193124, 197.35294117647072], "p2": [564.6315789473683, 193.80382775119642], "p3": [564.7263922518159, 231.27360774818408], "p4": [284.73041226193124, 235.7212765957448]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": false, "content": [{"bbox": null, "direction": null, "text": "(a+b)+c=a+(b+c)", "score": null}], "bbox": {"p1": [13.567810457516316, 139.625], "p2": [284.73041226193124, 135.8235294117644], "p3": [284.73041226193124, 174.22352941176467], "p4": [13.125, 174.22352941176467]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "a+b=b+a", "score": null}], "bbox": {"p1": [13.0, 200.0], "p2": [284.73041226193124, 197.35294117647072], "p3": [284.73041226193124, 235.7212765957448], "p4": [13.063829787234226, 235.7212765957448]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [{"bbox": null, "direction": null, "text": "a·(b+c)=(a·b)+(a·c)", "score": null}], "bbox": {"p1": [8.0, 580.2727272727275], "p2": [284.73041226193124, 583.4630872483222], "p3": [284.73041226193124, 625.2684563758389], "p4": [8.0, 622.0]}, "lloc": {"start_row": 16, "end_row": 16, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": false, "content": [{"bbox": null, "direction": null, "text": "(3·5)·7=3·(5·7)", "score": null}], "bbox": {"p1": [284.73041226193124, 320.59334054634326], "p2": [564.5157384987892, 320.59334054634326], "p3": [563.0, 360.7950304259635], "p4": [284.73041226193124, 360.7950304259635]}, "lloc": {"start_row": 8, "end_row": 8, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": false, "content": [{"bbox": null, "direction": null, "text": "2·(4+3)=(2·4)+(2·3)", "score": null}], "bbox": {"p1": [284.73041226193124, 583.4630872483222], "p2": [568.4557823129248, 587.5034013605441], "p3": [568.8639455782313, 627.7278911564626], "p4": [284.73041226193124, 625.2684563758389]}, "lloc": {"start_row": 16, "end_row": 16, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": false, "content": [{"bbox": null, "direction": null, "text": "5·0=0·5=0", "score": null}], "bbox": {"p1": [284.73041226193124, 519.7119785686949], "p2": [567.0256410256407, 519.7119785686949], "p3": [568.17094017094, 562.8290598290596], "p4": [284.73041226193124, 558.4827121889914]}, "lloc": {"start_row": 14, "end_row": 14, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": true, "content": [{"bbox": null, "direction": null, "text": "Properties of Operations", "score": null}], "bbox": {"p1": [15.0, 83.0], "p2": [564.0, 76.0], "p3": [564.7177033492821, 108.23923444976072], "p4": [13.0, 115.5]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 9, "header": false, "content": [{"bbox": null, "direction": null, "text": "a·0=0·a=0", "score": null}], "bbox": {"p1": [9.078651685392742, 517.0], "p2": [284.73041226193124, 519.7119785686949], "p3": [284.73041226193124, 558.4827121889914], "p4": [8.674157303370976, 558.4827121889914]}, "lloc": {"start_row": 14, "end_row": 14, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 10, "header": false, "content": [{"bbox": null, "direction": null, "text": "3+0=0+3=3", "score": null}], "bbox": {"p1": [284.73041226193124, 257.7604395604394], "p2": [563.4527845036318, 257.7604395604394], "p3": [563.7893462469733, 295.6202292259797], "p4": [284.73041226193124, 295.6202292259797]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 11, "header": false, "content": [{"bbox": null, "direction": null, "text": "(a·b)·c=a·(b·c)", "score": null}], "bbox": {"p1": [11.808625336927435, 320.59334054634326], "p2": [284.73041226193124, 320.59334054634326], "p3": [284.73041226193124, 360.7950304259635], "p4": [10.799999999999727, 360.7950304259635]}, "lloc": {"start_row": 8, "end_row": 8, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 12, "header": false, "content": [{"bbox": null, "direction": null, "text": "Associative Property of Addition", "score": null}], "bbox": {"p1": [13.0, 115.5], "p2": [564.7177033492821, 108.23923444976072], "p3": [563.7607655502388, 131.0], "p4": [13.567810457516316, 139.625]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 13, "header": false, "content": [{"bbox": null, "direction": null, "text": "Commutative Property of Addition", "score": null}], "bbox": {"p1": [13.125, 174.22352941176467], "p2": [564.7177033492826, 170.47846889952189], "p3": [564.6315789473683, 193.80382775119642], "p4": [13.0, 200.0]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 0, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 14, "header": false, "content": [{"bbox": null, "direction": null, "text": "a+0=0+a=a", "score": null}], "bbox": {"p1": [13.061702127659373, 260.5957446808511], "p2": [284.73041226193124, 257.7604395604394], "p3": [284.73041226193124, 295.6202292259797], "p4": [11.799999999999727, 295.6202292259797]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 15, "header": false, "content": [{"bbox": null, "direction": null, "text": "ldentity Property of Addition", "score": null}], "bbox": {"p1": [13.063829787234226, 235.7212765957448], "p2": [564.7263922518159, 231.27360774818408], "p3": [563.4527845036318, 257.7604395604394], "p4": [13.061702127659373, 260.5957446808511]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 0, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 16, "header": false, "content": [{"bbox": null, "direction": null, "text": "a·1=1·a=a", "score": null}], "bbox": {"p1": [10.545253863134803, 448.33774834437054], "p2": [284.73041226193124, 451.2305934288278], "p3": [284.73041226193124, 492.87230141075236], "p4": [9.22471910112381, 492.87230141075236]}, "lloc": {"start_row": 12, "end_row": 12, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 17, "header": false, "content": [{"bbox": null, "direction": null, "text": "Associative Property of Multiplication", "score": null}], "bbox": {"p1": [11.799999999999727, 295.6202292259797], "p2": [563.7893462469733, 295.6202292259797], "p3": [564.5157384987892, 320.59334054634326], "p4": [11.808625336927435, 320.59334054634326]}, "lloc": {"start_row": 7, "end_row": 7, "start_col": 0, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 18, "header": false, "content": [{"bbox": null, "direction": null, "text": "Commutative Property of Multiplication", "score": null}], "bbox": {"p1": [10.799999999999727, 360.7950304259635], "p2": [563.0, 360.7950304259635], "p3": [564.4444444444443, 386.18413701803513], "p4": [10.285906406427785, 386.18413701803513]}, "lloc": {"start_row": 9, "end_row": 9, "start_col": 0, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 19, "header": false, "content": [{"bbox": null, "direction": null, "text": "8·1=1·8=8", "score": null}], "bbox": {"p1": [284.73041226193124, 451.2305934288278], "p2": [566.8597475455817, 451.2305934288278], "p3": [566.931623931624, 492.87230141075236], "p4": [284.73041226193124, 492.87230141075236]}, "lloc": {"start_row": 12, "end_row": 12, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 20, "header": false, "content": [{"bbox": null, "direction": null, "text": "Zero Property of Multiplication", "score": null}], "bbox": {"p1": [9.22471910112381, 492.87230141075236], "p2": [566.931623931624, 492.87230141075236], "p3": [567.0256410256407, 519.7119785686949], "p4": [9.078651685392742, 517.0]}, "lloc": {"start_row": 13, "end_row": 13, "start_col": 0, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 21, "header": false, "content": [{"bbox": null, "direction": null, "text": "6·3=3·6", "score": null}], "bbox": {"p1": [284.73041226193124, 386.18413701803513], "p2": [564.4444444444443, 386.18413701803513], "p3": [566.5610098176717, 426.26316862155335], "p4": [284.73041226193124, 426.26316862155335]}, "lloc": {"start_row": 10, "end_row": 10, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 22, "header": false, "content": [{"bbox": null, "direction": null, "text": "Distributive Property of Multiplication over Addition", "score": null}], "bbox": {"p1": [8.674157303370976, 558.4827121889914], "p2": [568.17094017094, 562.8290598290596], "p3": [568.4557823129248, 587.5034013605441], "p4": [8.0, 580.2727272727275]}, "lloc": {"start_row": 15, "end_row": 15, "start_col": 0, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 23, "header": false, "content": [{"bbox": null, "direction": null, "text": "a·b=b·a", "score": null}], "bbox": {"p1": [10.285906406427785, 386.18413701803513], "p2": [284.73041226193124, 386.18413701803513], "p3": [284.73041226193124, 426.26316862155335], "p4": [9.883006535881577, 426.26316862155335]}, "lloc": {"start_row": 10, "end_row": 10, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 24, "header": false, "content": [{"bbox": null, "direction": null, "text": "Identity Property of Multiplication", "score": null}], "bbox": {"p1": [9.883006535881577, 426.26316862155335], "p2": [566.5610098176717, 426.26316862155335], "p3": [566.8597475455817, 451.2305934288278], "p4": [10.545253863134803, 448.33774834437054]}, "lloc": {"start_row": 11, "end_row": 11, "start_col": 0, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}