{"table_ind": 0, "image_path": "", "type": 1, "cells": [{"cell_ind": 0, "header": false, "content": [{"bbox": null, "direction": null, "text": "无", "score": null}], "bbox": {"p1": [152.3865966796875, 415.0994873046875], "p2": [559.586181640625, 422.84800415039064], "p3": [554.9547932942709, 644.479248046875], "p4": [148.10299682617188, 637.0]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "网页中所有DOM结构绘制完毕后就执行，可能DOM\n元素关联的东西并没有加载完", "score": null}], "bbox": {"p1": [567.3796997070312, 70.14994812011719], "p2": [1002.0875854492188, 67.03065490722656], "p3": [1002.6253051757812, 143.7212425771385], "p4": [568.9870834350586, 143.7212425771385]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": true, "content": [{"bbox": null, "direction": null, "text": "$(document).ready()", "score": null}], "bbox": {"p1": [568.9870834350586, 35.37250900268555], "p2": [1001.8251953125, 30.491130828857422], "p3": [1002.4072265625, 82.11863708496094], "p4": [568.9870834350586, 87.29859670003255]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "必须等待网页中所有的内容加载完毕后 （包括\n图片）才能执行", "score": null}], "bbox": {"p1": [180.3351535989782, 84.55798617276287], "p2": [568.9870834350586, 87.29859670003255], "p3": [568.9870834350586, 148.78619384765625], "p4": [180.3351535989782, 146.333984375]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [{"bbox": null, "direction": null, "text": "不能同时编写多个\n一下代码无法正确执行：window. onload = function(){\nalert(\"testl\")\n} :\nwindow. onload = function(){\nalert(\"test2\")\n} :\n结果只会输出 “test2”", "score": null}], "bbox": {"p1": [166.8234405517578, 139.60525512695312], "p2": [568.9870834350586, 143.7212425771385], "p3": [564.1285400390625, 422.84800415039064], "p4": [162.89068094889322, 417.1379089355469]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": false, "content": [{"bbox": null, "direction": null, "text": "执行时机", "score": null}], "bbox": {"p1": [49.000003814697266, 79.0], "p2": [184.0005590265448, 84.55798617276287], "p3": [180.3351535989782, 143.7212425771385], "p4": [47.0, 137.0]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": true, "content": [{"bbox": null, "direction": null, "text": "window. onload", "score": null}], "bbox": {"p1": [184.0005590265448, 51.99706268310547], "p2": [568.9870834350586, 52.388893127441406], "p3": [568.9870834350586, 92.11759948730469], "p4": [184.0005590265448, 92.11759948730469]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": false, "content": [{"bbox": null, "direction": null, "text": "$(document) . ready(function(){\n//...\n}):\n可以简写成：\n$(function(){\n//...\n}):", "score": null}], "bbox": {"p1": [554.9547932942709, 422.84800415039064], "p2": [1004.5556335449219, 426.1522521972656], "p3": [1002.8492431640625, 660.0000610351562], "p4": [554.9547932942709, 656.3660278320312]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": false, "content": [{"bbox": null, "direction": null, "text": "简化写法", "score": null}], "bbox": {"p1": [21.29981231689453, 407.64593505859375], "p2": [162.89068094889322, 417.1379089355469], "p3": [148.10299682617188, 643.1930541992188], "p4": [6.160926818847656, 633.7011108398438]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 9, "header": true, "content": [{"bbox": null, "direction": null, "text": "", "score": null}], "bbox": {"p1": [51.358009338378906, 43.104225158691406], "p2": [186.77777099609375, 52.0], "p3": [184.0005590265448, 87.29859670003255], "p4": [49.000003814697266, 79.0]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 10, "header": false, "content": [{"bbox": null, "direction": null, "text": "能同时编写多个\n以下代码正确执行:\n$(document) . ready(function(){\nalert(\"Hello World!\");\n});\n$(document) . ready(function(){\nalert(\"Hello World!\");\n});结果两次都输出", "score": null}], "bbox": {"p1": [561.9721069335938, 127.68141174316406], "p2": [1006.7557983398438, 131.2803955078125], "p3": [1004.5556335449219, 426.1522521972656], "p4": [559.586181640625, 422.84800415039064]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 11, "header": false, "content": [{"bbox": null, "direction": null, "text": "编写个数", "score": null}], "bbox": {"p1": [39.000755310058594, 134.36456298828125], "p2": [180.3351535989782, 143.7212425771385], "p3": [162.89068094889322, 422.84800415039064], "p4": [21.35631561279297, 415.0994873046875]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}