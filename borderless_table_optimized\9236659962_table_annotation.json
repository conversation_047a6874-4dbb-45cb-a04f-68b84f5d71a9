{"table_ind": "9236659962", "image_path": "9236659962.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": false, "content": [{"bbox": null, "direction": null, "text": "Name of the member:(IN CAPITAL LETTERS)", "score": null}], "bbox": {"p1": [35.**************, 160.41871809759056], "p2": [331.*************, 166.32558139534854], "p3": [331.*************, 189.**************], "p4": [35.**************, 182.*************]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "Aadhar Number:", "score": null}], "bbox": {"p1": [35.**************, 204.**************], "p2": [331.*************, 211.**************], "p3": [331.*************, 234.*************2], "p4": [35.**************, 227.**************]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": false, "content": [{"bbox": null, "direction": null, "text": "Universal Account Number(UAN)", "score": null}], "bbox": {"p1": [35.**************, 182.*************], "p2": [331.*************, 189.**************], "p3": [331.*************, 211.**************], "p4": [35.**************, 204.**************]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [], "bbox": {"p1": [331.*************, 189.**************], "p2": [585.*************, 193.0], "p3": [585.0, 215.0], "p4": [331.*************, 211.**************]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [], "bbox": {"p1": [331.*************, 211.**************], "p2": [585.0, 215.0], "p3": [585.*************, 238.*************2], "p4": [331.*************, 234.*************2]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": false, "content": [{"bbox": null, "direction": null, "text": "Date of joining the establishment:", "score": null}], "bbox": {"p1": [35.**************, 227.**************], "p2": [331.*************, 234.*************2], "p3": [331.*************, 253.65264514423552], "p4": [35.**************, 248.26143790849673]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": false, "content": [], "bbox": {"p1": [331.*************, 166.32558139534854], "p2": [585.5993774414062, 171.23972167968745], "p3": [585.*************, 193.0], "p4": [331.*************, 189.**************]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": true, "content": [{"bbox": null, "direction": null, "text": "Clalm applied for:l) Final PF Settlernent () ii) Pension Withdrawal Benefits () iii) PF PART WITHDRAWAL()\n(Tick whichever is/are applicable)", "score": null}], "bbox": {"p1": [35.**************, 137.98039993585326], "p2": [586.0374755859375, 148.65682983398438], "p3": [585.5993774414062, 171.23972167968745], "p4": [35.**************, 160.41871809759056]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": false, "content": [{"bbox": null, "direction": null, "text": "a) Purpose of PF PART Withdrawal:(Tick v whichever applicabie)\nb) Amount (in Rs.):_____\nc) For purpose of Site/House/Flat or Construction through\"agency\"or\nRepayment of Housing Loan or LIC,indicate chegue to be drawn\n\"in favour of\"and payee's address.", "score": null}], "bbox": {"p1": [35.**************, 248.26143790849673], "p2": [331.*************, 253.65264514423552], "p3": [331.*************, 378.0], "p4": [32.5, 377.0]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 9, "header": false, "content": [], "bbox": {"p1": [331.*************, 234.*************2], "p2": [585.*************, 238.*************2], "p3": [584.3884850792679, 258.24486138926954], "p4": [331.*************, 253.65264514423552]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 10, "header": false, "content": [{"bbox": null, "direction": null, "text": "4", "score": null}], "bbox": {"p1": [11.0, 204.**************], "p2": [35.**************, 204.**************], "p3": [35.**************, 227.**************], "p4": [11.0, 227.**************]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 11, "header": false, "content": [{"bbox": null, "direction": null, "text": "2", "score": null}], "bbox": {"p1": [12.588235294117567, 160.41871809759056], "p2": [35.**************, 160.41871809759056], "p3": [35.**************, 182.*************], "p4": [11.784313725489937, 182.*************]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 12, "header": true, "content": [{"bbox": null, "direction": null, "text": "1 1", "score": null}], "bbox": {"p1": [12.946651387827387, 138.18283677107206], "p2": [35.**************, 137.98039993585326], "p3": [35.**************, 160.41871809759056], "p4": [12.588235294117567, 160.41871809759056]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 13, "header": false, "content": [{"bbox": null, "direction": null, "text": "3", "score": null}], "bbox": {"p1": [11.784313725489937, 182.*************], "p2": [35.**************, 182.*************], "p3": [35.**************, 204.**************], "p4": [11.0, 204.**************]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 14, "header": false, "content": [{"bbox": null, "direction": null, "text": "6", "score": null}], "bbox": {"p1": [10.803921568627175, 248.26143790849673], "p2": [35.**************, 248.26143790849673], "p3": [32.5, 377.0], "p4": [9.602409638554036, 378.0]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 15, "header": false, "content": [{"bbox": null, "direction": null, "text": "5", "score": null}], "bbox": {"p1": [11.0, 227.**************], "p2": [35.**************, 227.**************], "p3": [35.**************, 248.26143790849673], "p4": [10.803921568627175, 248.26143790849673]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 16, "header": false, "content": [{"bbox": null, "direction": null, "text": "SN Purpose of PF Part Withdrawal √\nⅠ Housing Loan/Purchase of site/House/Flat\nor for Construction/Addition, alteration in\nexisting house/Repayment of Housing loan\n(para 668/688B/68BC) 20 35\nⅡ Lockout or dosure of factory(Para 68H)\nⅢ 18 Illness of member/family(para 68J) 22\nⅣ Marriage of self/son/daughter/brother/\nsister (Para 68K)\nⅤ 17\nB### M### 23", "score": null}], "bbox": {"p1": [331.*************, 253.65264514423552], "p2": [584.3884850792679, 258.24486138926954], "p3": [583.8910124612357, 377.8636363636365], "p4": [331.*************, 378.0]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 17, "header": false, "content": [{"bbox": null, "direction": null, "text": "17", "score": null}], "bbox": {"p1": [542.5054604712736, 353.22735782468845], "p2": [573.869158878505, 353.22735782468845], "p3": [573.3250150088261, 374.99416872440037], "p4": [542.5054604712736, 374.99416872440037]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 18, "header": false, "content": [{"bbox": null, "direction": null, "text": "18", "score": null}], "bbox": {"p1": [542.5054604712736, 330.743145743146], "p2": [574.233644859813, 330.743145743146], "p3": [574.102803738318, 342.0945242191963], "p4": [542.5054604712736, 342.0945242191963]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 19, "header": false, "content": [{"bbox": null, "direction": null, "text": "Marriage of self/son/daughter/brother/\nsister (Para 68K)", "score": null}], "bbox": {"p1": [362.23890225728445, 349.89648090807117], "p2": [542.5054604712736, 353.22735782468845], "p3": [542.5054604712736, 374.99416872440037], "p4": [362.23890225728445, 371.6114613178099]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 20, "header": false, "content": [{"bbox": null, "direction": null, "text": "20", "score": null}], "bbox": {"p1": [542.5054604712736, 288.4632044099971], "p2": [574.3575378392898, 288.4632044099971], "p3": [574.233644859813, 330.743145743146], "p4": [542.5054604712736, 330.743145743146]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 21, "header": true, "content": [{"bbox": null, "direction": null, "text": "Purpose of PF Part Withdrawal", "score": null}], "bbox": {"p1": [362.23890225728445, 273.3502873966669], "p2": [542.5054604712736, 276.99356878371475], "p3": [542.5054604712736, 288.4632044099971], "p4": [362.23890225728445, 284.85541192776185]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 22, "header": false, "content": [{"bbox": null, "direction": null, "text": "22", "score": null}], "bbox": {"p1": [542.5054604712736, 342.0945242191963], "p2": [574.102803738318, 342.0945242191963], "p3": [573.869158878505, 353.22735782468845], "p4": [542.5054604712736, 353.22735782468845]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 23, "header": false, "content": [{"bbox": null, "direction": null, "text": "23", "score": null}], "bbox": {"p1": [542.5054604712736, 374.99416872440037], "p2": [573.3250150088261, 374.99416872440037], "p3": [573.1872474260986, 378.0], "p4": [542.5054604712736, 378.0]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 24, "header": false, "content": [{"bbox": null, "direction": null, "text": "B### M###", "score": null}], "bbox": {"p1": [362.23890225728445, 371.6114613178099], "p2": [542.5054604712736, 374.99416872440037], "p3": [542.5054604712736, 378.0], "p4": [362.23890225728445, 378.0]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 25, "header": false, "content": [{"bbox": null, "direction": null, "text": "Illness of member/family(para 68J)", "score": null}], "bbox": {"p1": [362.23890225728445, 338.5885928673585], "p2": [542.5054604712736, 342.0945242191963], "p3": [542.5054604712736, 353.22735782468845], "p4": [362.23890225728445, 349.89648090807117]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 26, "header": false, "content": [{"bbox": null, "direction": null, "text": "Lockout or dosure of factory(Para 68H)", "score": null}], "bbox": {"p1": [362.23890225728445, 327.3315954118876], "p2": [542.5054604712736, 330.743145743146], "p3": [542.5054604712736, 342.0945242191963], "p4": [362.23890225728445, 338.5885928673585]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 27, "header": true, "content": [{"bbox": null, "direction": null, "text": "√", "score": null}], "bbox": {"p1": [542.5054604712736, 276.99356878371475], "p2": [575.2857142857142, 277.0212026416409], "p3": [574.3575378392898, 288.4632044099971], "p4": [542.5054604712736, 288.4632044099971]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 28, "header": false, "content": [{"bbox": null, "direction": null, "text": "Housing Loan/Purchase of site/House/Flat\nor for Construction/Addition, alteration in\nexisting house/Repayment of Housing loan\n(para 668/688B/68BC)\n35", "score": null}], "bbox": {"p1": [362.23890225728445, 284.85541192776185], "p2": [542.5054604712736, 288.4632044099971], "p3": [542.5054604712736, 330.743145743146], "p4": [362.23890225728445, 327.3315954118876]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 29, "header": true, "content": [{"bbox": null, "direction": null, "text": "SN", "score": null}], "bbox": {"p1": [340.7099567099576, 272.90476190476215], "p2": [362.23890225728445, 273.3502873966669], "p3": [362.23890225728445, 284.85541192776185], "p4": [340.6147186147191, 284.85541192776185]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 30, "header": false, "content": [{"bbox": null, "direction": null, "text": "Ⅰ", "score": null}], "bbox": {"p1": [340.6147186147191, 284.85541192776185], "p2": [362.23890225728445, 284.85541192776185], "p3": [362.23890225728445, 327.3315954118876], "p4": [339.6458523797014, 327.3315954118876]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 31, "header": false, "content": [{"bbox": null, "direction": null, "text": "Ⅱ", "score": null}], "bbox": {"p1": [339.6458523797014, 327.3315954118876], "p2": [362.23890225728445, 327.3315954118876], "p3": [362.23890225728445, 338.5885928673585], "p4": [339.4779134484038, 338.5885928673585]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 32, "header": false, "content": [{"bbox": null, "direction": null, "text": "Ⅲ", "score": null}], "bbox": {"p1": [339.4779134484038, 338.5885928673585], "p2": [362.23890225728445, 338.5885928673585], "p3": [362.23890225728445, 349.89648090807117], "p4": [339.67273030644446, 349.89648090807117]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 33, "header": false, "content": [{"bbox": null, "direction": null, "text": "Ⅳ", "score": null}], "bbox": {"p1": [339.67273030644446, 349.89648090807117], "p2": [362.23890225728445, 349.89648090807117], "p3": [362.23890225728445, 371.6114613178099], "p4": [338.9640884728533, 371.6114613178099]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 34, "header": false, "content": [{"bbox": null, "direction": null, "text": "Ⅴ", "score": null}], "bbox": {"p1": [338.9640884728533, 371.6114613178099], "p2": [362.23890225728445, 371.6114613178099], "p3": [362.23890225728445, 378.0], "p4": [338.88616465567657, 378.0]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}