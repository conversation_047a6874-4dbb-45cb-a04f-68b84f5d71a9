{"table_ind": "ia_100000021094_4", "image_path": "ia_100000021094_4.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": false, "content": [{"bbox": null, "direction": null, "text": "4= at least 6 instructions without\nprompts and can follow at least 4\ndifferent aclions within 10\nseconds 3= at least 6\ninstructions without prompts 2=at\nleast 4 instructions without\nprompts, 1=at least 2 instructions\nwithout prompts", "score": null}], "bbox": {"p1": [692.8992568614876, 290.6926545015104], "p2": [877.2697143554688, 285.9069549317143], "p3": [879.532926057514, 415.1776496485661], "p4": [695.1626586914062, 418.3512768887057]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 6, "end_col": 6}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "2= usually within 3 seconds, 1=\nusually with one additional prompt\n(not including holding out hand to\no receive the item)", "score": null}], "bbox": {"p1": [688.4032258064517, 194.86228927391468], "p2": [862.7465654142679, 194.86228927391468], "p3": [869.271656271656, 285.9069549317143], "p4": [692.8992568614876, 290.6926545015104]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 6, "end_col": 6}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": false, "content": [{"bbox": null, "direction": null, "text": "Modified. Note: This tsk does\nstudent to be able to receptively\nof the item to select it from a gr", "score": null}], "bbox": {"p1": [862.7465654142679, 194.86228927391468], "p2": [1024.0, 192.80000000000018], "p3": [1024.0, 285.9069549317143], "p4": [869.271656271656, 285.9069549317143]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 7, "end_col": 7}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "2= readily finds and selects it in\nany position within 3 seconds, 1=\nfinds and selects item if it is held\nin front of him", "score": null}], "bbox": {"p1": [708.6720194526047, 582.2207505518768], "p2": [894.5942834754378, 579.6203473945407], "p3": [907.8512457614625, 708.4524886877834], "p4": [718.0961209842781, 713.556818181818]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 6, "end_col": 6}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [{"bbox": null, "direction": null, "text": "4=at least 3 activities without\nprompts, 3= at least 3 with only\none verbal or pointing prompt. 2=\n3 or more with a model and no\nphysical prompts, 1= 3 or more\nactivities with only partial physical\nprompts", "score": null}], "bbox": {"p1": [684.1942257217848, 92.92715581253167], "p2": [855.8040153860593, 92.92715581253167], "p3": [862.7465654142679, 194.86228927391468], "p4": [688.4032258064517, 194.86228927391468]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 6, "end_col": 6}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": false, "content": [{"bbox": null, "direction": null, "text": "Jump, sit down, arms\nup, clap, blow, knock.", "score": null}], "bbox": {"p1": [563.1598513011159, 290.6926545015104], "p2": [692.8992568614876, 290.6926545015104], "p3": [700.0, 418.3512768887056], "p4": [566.468271334792, 420.65095293267575]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 5, "end_col": 5}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": false, "content": [{"bbox": null, "direction": null, "text": "When a shoe is held an\none side, and a bottle of\nbubbles (a reinforcer) is\nheld in a different\nposition, the student will\nselect \"bubbles\" upon\nrequest.", "score": null}], "bbox": {"p1": [572.1513157894733, 584.8223684210525], "p2": [708.6720194526047, 582.2207505518768], "p3": [718.0961209842781, 713.556818181818], "p4": [577.4610389610389, 717.3246753246749]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 5, "end_col": 5}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": false, "content": [{"bbox": null, "direction": null, "text": "2= readily finds and touches it in\nany position within 3 seconds, 1=\ns touches an item if it is held in front\nof him", "score": null}], "bbox": {"p1": [700.0, 418.3512768887056], "p2": [879.532926057514, 415.1776496485661], "p3": [894.5942834754378, 579.6203473945407], "p4": [708.6720194526047, 582.2207505518768]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 6, "end_col": 6}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": false, "content": [{"bbox": null, "direction": null, "text": "The student will comply with\nthe instructions to do a non-\npreferred activity when the\nactivity is presently occurring.", "score": null}], "bbox": {"p1": [268.0289368473979, 90.54450746737545], "p2": [429.26574984548375, 90.54450746737545], "p3": [429.26574984548375, 194.86228927391468], "p4": [268.0289368473979, 194.86228927391468]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 9, "header": false, "content": [{"bbox": null, "direction": null, "text": "If you hold up or place\ntwo items in any position\nnear the student (one\nbeing a reinforcer) and\nask him to select the\nreinforcer, can he do it?", "score": null}], "bbox": {"p1": [429.26574984548375, 588.8031409427222], "p2": [572.1513157894733, 584.8223684210525], "p3": [577.4610389610389, 717.3246753246749], "p4": [429.26574984548375, 721.6860282574562]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 10, "header": false, "content": [{"bbox": null, "direction": null, "text": "Upon request the child\nwill hand a shoe to an\ninstructor.", "score": null}], "bbox": {"p1": [560.698340874812, 196.20127530945666], "p2": [688.4032258064517, 194.86228927391468], "p3": [692.8992568614876, 290.6926545015104], "p4": [563.1598513011159, 290.6926545015104]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 5, "end_col": 5}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 11, "header": false, "content": [{"bbox": null, "direction": null, "text": "When an adult holds out\nhis hand and asks for an\nitem, will the student\nfollow instructions to give\na named, non-desired\ntem?", "score": null}], "bbox": {"p1": [429.26574984548375, 194.86228927391468], "p2": [560.698340874812, 196.20127530945666], "p3": [563.1598513011159, 290.6926545015104], "p4": [429.26574984548375, 290.6926545015104]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 12, "header": false, "content": [{"bbox": null, "direction": null, "text": "The student will comply with\ninstructions to do a simple\nmotor task (e.g., clap, turn\naround, arms up).", "score": null}], "bbox": {"p1": [264.4868786184734, 290.6926545015104], "p2": [429.26574984548375, 290.6926545015104], "p3": [429.26574984548375, 420.65095293267575], "p4": [264.4868786184734, 424.0]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 13, "header": false, "content": [{"bbox": null, "direction": null, "text": "Will the student follow an\ninstruction to do a non-\npreferred activity at the\ntime when the activity\nusually occurs (e.g.,\n\"Wash hands' before\nlunch)?", "score": null}], "bbox": {"p1": [426.51903114186854, 90.54450746737545], "p2": [557.5784883720929, 92.92715581253167], "p3": [560.698340874812, 196.20127530945666], "p4": [429.26574984548375, 194.86228927391468]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 14, "header": false, "content": [{"bbox": null, "direction": null, "text": "If you hold up an object\nin one hand and hold up\nyour other empty hand\n(or the object is placed\non a table with another\nneutral item such a\nstyrofoam cube used as\na distracter) will the\nstudent touch the named\nobject when instructed?", "score": null}], "bbox": {"p1": [429.26574984548375, 420.65095293267575], "p2": [566.468271334792, 420.65095293267575], "p3": [572.1513157894733, 584.8223684210525], "p4": [429.26574984548375, 588.8031409427222]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 15, "header": false, "content": [{"bbox": null, "direction": null, "text": "Will the student follow an\ninstruction to do a simple\nmotor action upon\nrequest (e.g., \"Clap\nhands\")?", "score": null}], "bbox": {"p1": [429.26574984548375, 290.6926545015104], "p2": [563.1598513011159, 290.6926545015104], "p3": [566.468271334792, 420.65095293267575], "p4": [429.26574984548375, 420.65095293267575]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 16, "header": false, "content": [{"bbox": null, "direction": null, "text": "Modified. See Appendix 2: S\nList", "score": null}], "bbox": {"p1": [869.271656271656, 285.9069549317143], "p2": [1024.0, 285.9069549317143], "p3": [1024.0, 412.67597765363143], "p4": [879.532926057514, 415.1776496485661]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 7, "end_col": 7}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 17, "header": false, "content": [{"bbox": null, "direction": null, "text": "When asked to touch an\nobject that is being held and\nan empty hand is held out as\na distracter (or the object is\nplaced on a table with another\nneutral item such a Styrofoam\ncube used as a distracter), the\nstudent will touch the named\nobject.", "score": null}], "bbox": {"p1": [264.4868786184734, 424.0], "p2": [429.26574984548375, 420.65095293267575], "p3": [429.26574984548375, 588.8031409427222], "p4": [257.55595981859574, 592.7966458978206]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 18, "header": false, "content": [{"bbox": null, "direction": null, "text": "Modified", "score": null}], "bbox": {"p1": [855.8040153860593, 92.92715581253167], "p2": [1024.0, 90.54450746737545], "p3": [1024.0, 192.80000000000018], "p4": [862.7465654142679, 194.86228927391468]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 7, "end_col": 7}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 19, "header": false, "content": [{"bbox": null, "direction": null, "text": "After using the toilet, the\nstudent will follow\ndirections to \"wash\nhands.\"", "score": null}], "bbox": {"p1": [557.5784883720929, 92.92715581253167], "p2": [684.1942257217848, 92.92715581253167], "p3": [688.4032258064517, 194.86228927391468], "p4": [560.698340874812, 196.20127530945666]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 5, "end_col": 5}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 20, "header": false, "content": [{"bbox": null, "direction": null, "text": "When asked, the student will\nselect a named reinforcing\nitem from an array of two\nobjects held or placed in any\nposition in front of him..", "score": null}], "bbox": {"p1": [257.55595981859574, 592.7966458978206], "p2": [429.26574984548375, 588.8031409427222], "p3": [429.26574984548375, 721.6860282574562], "p4": [252.3145276037735, 727.4869888475832]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 21, "header": false, "content": [{"bbox": null, "direction": null, "text": "The student will follow\ninstructions which require him\nto give a named, non-\nenforcing item.", "score": null}], "bbox": {"p1": [268.0289368473979, 194.86228927391468], "p2": [429.26574984548375, 194.86228927391468], "p3": [429.26574984548375, 290.6926545015104], "p4": [264.4868786184734, 290.6926545015104]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 22, "header": false, "content": [{"bbox": null, "direction": null, "text": "When a cup is held on\none side, and an empty\nhand is held in a\ndifferent position, the\nstudent will select \"cup\"\nupon request.", "score": null}], "bbox": {"p1": [566.468271334792, 420.65095293267575], "p2": [700.0, 418.3512768887056], "p3": [708.6720194526047, 582.2207505518768], "p4": [572.1513157894733, 584.8223684210525]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 5, "end_col": 5}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 23, "header": false, "content": [{"bbox": null, "direction": null, "text": "Follow\ninstructions to\ndo a simple\nmotor action", "score": null}], "bbox": {"p1": [172.1935483870966, 290.6926545015104], "p2": [264.4868786184734, 290.6926545015104], "p3": [264.4868786184734, 424.0], "p4": [168.879194630872, 425.81208053691273]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 24, "header": false, "content": [{"bbox": null, "direction": null, "text": "Follow\ninstructions to\nselect one\nreinforcing item\nfrom an array of\ntwo objects", "score": null}], "bbox": {"p1": [156.54232788085938, 588.8031409427222], "p2": [257.55595981859574, 592.7966458978206], "p3": [252.3145276037735, 734.2221069335938], "p4": [151.17302759791437, 730.3870603499872]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 25, "header": false, "content": [{"bbox": null, "direction": null, "text": "Follow\ninstructions in\nroutine\nsituations", "score": null}], "bbox": {"p1": [179.41578924809951, 90.54450746737545], "p2": [268.0289368473979, 90.54450746737545], "p3": [268.0289368473979, 194.86228927391468], "p4": [175.82383335588838, 194.86228927391468]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 26, "header": false, "content": [{"bbox": null, "direction": null, "text": "Follow\ninstructions to\ntouch item vs. a\ndistracter", "score": null}], "bbox": {"p1": [168.879194630872, 425.81208053691273], "p2": [264.4868786184734, 424.0], "p3": [257.55595981859574, 592.7966458978206], "p4": [159.58224163027717, 596.2115477923339]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 27, "header": false, "content": [{"bbox": null, "direction": null, "text": "Modified. NOTE: The rece\nskills for tasks C 10-C 17\nwith any type of selection re\nto, pick up, give me, touch,\nfind the_)", "score": null}], "bbox": {"p1": [879.532926057514, 415.1776496485661], "p2": [1024.0, 412.67597765363143], "p3": [1024.0, 576.3274336283189], "p4": [894.5942834754378, 579.6203473945407]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 7, "end_col": 7}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 28, "header": false, "content": [{"bbox": null, "direction": null, "text": "0 1 2\n0 1 2\n0 1 2\n0 1 2", "score": null}], "bbox": {"p1": [96.67103347889406, 596.2115477923339], "p2": [159.58224163027717, 596.2115477923339], "p3": [151.17302759791437, 730.3870603499872], "p4": [85.87976539589454, 731.9061583577713]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 29, "header": false, "content": [{"bbox": null, "direction": null, "text": "0 1 2\n0 1 2\n0 1 2\n0 1 2", "score": null}], "bbox": {"p1": [108.73154362416153, 425.81208053691273], "p2": [168.879194630872, 425.81208053691273], "p3": [159.58224163027717, 596.2115477923339], "p4": [96.67103347889406, 596.2115477923339]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 30, "header": false, "content": [{"bbox": null, "direction": null, "text": "C 7", "score": null}], "bbox": {"p1": [81.92595945143648, 90.54450746737545], "p2": [122.45350497702232, 90.54450746737545], "p3": [118.17317059012885, 196.20127530945666], "p4": [77.06963589164252, 196.20127530945666]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 31, "header": false, "content": [{"bbox": null, "direction": null, "text": "C 10", "score": null}], "bbox": {"p1": [66.46308724832215, 425.81208053691273], "p2": [108.73154362416153, 425.81208053691273], "p3": [96.67103347889406, 596.2115477923339], "p4": [52.41775836972283, 596.2115477923339]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 32, "header": false, "content": [{"bbox": null, "direction": null, "text": "Follow\ninstructions to\ngive a named,\nnon-reinforcing\nobject", "score": null}], "bbox": {"p1": [175.82383335588838, 194.86228927391468], "p2": [268.0289368473979, 194.86228927391468], "p3": [264.4868786184734, 290.6926545015104], "p4": [172.1935483870966, 290.6926545015104]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 33, "header": true, "content": [{"bbox": null, "direction": null, "text": "CRITERIA", "score": null}], "bbox": {"p1": [684.1942257217848, 55.31496062992119], "p2": [852.9803921568637, 54.627450980391586], "p3": [855.8040153860593, 92.92715581253167], "p4": [684.1942257217848, 92.92715581253167]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 6, "end_col": 6}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 34, "header": false, "content": [{"bbox": null, "direction": null, "text": "0 1 2 3 4\n0 1 2 3 4\n0 1 2 3 4\n0 1 2 3 4", "score": null}], "bbox": {"p1": [122.45350497702232, 90.54450746737545], "p2": [179.41578924809951, 90.54450746737545], "p3": [175.82383335588838, 196.20127530945666], "p4": [118.17317059012885, 194.86228927391468]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 35, "header": true, "content": [{"bbox": null, "direction": null, "text": "QUESTION", "score": null}], "bbox": {"p1": [426.5190311418686, 53.480968858131746], "p2": [557.5784883720929, 55.125], "p3": [557.5784883720929, 92.92715581253167], "p4": [426.51903114186854, 90.54450746737545]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 4, "end_col": 4}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 36, "header": false, "content": [{"bbox": null, "direction": null, "text": "0 1 2\n0 1 2\n0 1 2\n0 1 2", "score": null}], "bbox": {"p1": [118.17317059012885, 194.86228927391468], "p2": [175.82383335588838, 194.86228927391468], "p3": [172.1935483870966, 290.6926545015104], "p4": [114.0, 290.6926545015104]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 37, "header": false, "content": [{"bbox": null, "direction": null, "text": "C 8", "score": null}], "bbox": {"p1": [77.06963589164252, 196.20127530945666], "p2": [118.17317059012885, 194.86228927391468], "p3": [114.0, 290.6926545015104], "p4": [73.0, 290.6926545015104]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 38, "header": false, "content": [{"bbox": null, "direction": null, "text": "0 1 2 3 4\n0 1 2 3 4\n0 1 2 3 4\n0 1 2 3 4", "score": null}], "bbox": {"p1": [114.0, 290.6926545015104], "p2": [172.1935483870966, 290.6926545015104], "p3": [168.879194630872, 425.81208053691273], "p4": [108.73154362416153, 425.81208053691273]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 39, "header": true, "content": [{"bbox": null, "direction": null, "text": "NOTES", "score": null}], "bbox": {"p1": [852.9803921568637, 54.627450980391586], "p2": [1024.0, 54.78431372549039], "p3": [1024.0, 90.54450746737545], "p4": [855.8040153860593, 92.92715581253167]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 7, "end_col": 7}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 40, "header": false, "content": [{"bbox": null, "direction": null, "text": "Modified. See Appendi\nLabei List", "score": null}], "bbox": {"p1": [894.5942834754378, 579.6203473945407], "p2": [1024.0, 576.3274336283189], "p3": [1023.0, 703.0], "p4": [907.8512457614625, 708.4524886877834]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 7, "end_col": 7}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 41, "header": true, "content": [{"bbox": null, "direction": null, "text": "EXAMPLES", "score": null}], "bbox": {"p1": [557.5784883720929, 55.125], "p2": [684.1942257217848, 55.31496062992119], "p3": [684.1942257217848, 92.92715581253167], "p4": [557.5784883720929, 92.92715581253167]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 5, "end_col": 5}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 42, "header": false, "content": [{"bbox": null, "direction": null, "text": "C 9", "score": null}], "bbox": {"p1": [73.0, 290.6926545015104], "p2": [114.0, 290.6926545015104], "p3": [108.73154362416153, 425.81208053691273], "p4": [66.46308724832215, 425.81208053691273]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 43, "header": false, "content": [{"bbox": null, "direction": null, "text": "C 11", "score": null}], "bbox": {"p1": [52.41775836972283, 596.2115477923339], "p2": [96.67103347889406, 596.2115477923339], "p3": [85.87976539589454, 731.9061583577713], "p4": [40.68035190615865, 733.7859237536659]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 44, "header": true, "content": [{"bbox": null, "direction": null, "text": "TASK OBJECTIVE", "score": null}], "bbox": {"p1": [268.0289368473979, 52.323212263963796], "p2": [426.5190311418686, 53.480968858131746], "p3": [426.51903114186854, 90.54450746737545], "p4": [268.0289368473979, 90.54450746737545]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 45, "header": true, "content": [{"bbox": null, "direction": null, "text": "TASK NAME", "score": null}], "bbox": {"p1": [179.41578924809951, 52.77671682773234], "p2": [268.0289368473979, 52.323212263963796], "p3": [268.0289368473979, 90.54450746737545], "p4": [179.41578924809951, 90.54450746737545]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 46, "header": true, "content": [{"bbox": null, "direction": null, "text": "SCORE", "score": null}], "bbox": {"p1": [122.45350497702232, 50.859717848273704], "p2": [179.41578924809951, 52.77671682773234], "p3": [179.41578924809951, 90.54450746737545], "p4": [122.45350497702232, 90.54450746737545]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 47, "header": true, "content": [{"bbox": null, "direction": null, "text": "TASK", "score": null}], "bbox": {"p1": [82.2771078167807, 51.00051207210072], "p2": [122.45350497702232, 50.859717848273704], "p3": [122.45350497702232, 90.54450746737545], "p4": [81.92595945143648, 90.54450746737545]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}