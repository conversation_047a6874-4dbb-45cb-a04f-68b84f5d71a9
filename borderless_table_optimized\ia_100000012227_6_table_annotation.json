{"table_ind": "ia_100000012227_6", "image_path": "ia_100000012227_6.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": true, "content": [{"bbox": null, "direction": null, "text": "Fuse amp rating", "score": null}], "bbox": {"p1": [72.5372617015567, 225.099365234375], "p2": [225.2005979714184, 222.41400146484375], "p3": [225.2005979714184, 250.7377131464218], "p4": [72.5372617015567, 252.43657145674783]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "10A", "score": null}], "bbox": {"p1": [72.5372617015567, 252.43657145674783], "p2": [225.2005979714184, 250.7377131464218], "p3": [225.2005979714184, 275.287837187491], "p4": [72.5372617015567, 279.22190748488924]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": false, "content": [{"bbox": null, "direction": null, "text": "10A", "score": null}], "bbox": {"p1": [72.5372617015567, 279.22190748488924], "p2": [225.2005979714184, 275.287837187491], "p3": [230.30503238846347, 348.3613771713203], "p4": [74.8729000739293, 353.195412877819]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "5A", "score": null}], "bbox": {"p1": [74.8729000739293, 353.195412877819], "p2": [230.30503238846347, 348.3613771713203], "p3": [230.30503238846347, 373.8015597672247], "p4": [77.39676085759493, 380.84502740592933]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [{"bbox": null, "direction": null, "text": "10A", "score": null}], "bbox": {"p1": [77.39676085759493, 380.84502740592933], "p2": [230.30503238846347, 373.8015597672247], "p3": [230.30503238846347, 414.7439135538566], "p4": [79.33934429206313, 421.19921500915825]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": false, "content": [{"bbox": null, "direction": null, "text": "10A", "score": null}], "bbox": {"p1": [79.33934429206313, 421.19921500915825], "p2": [230.30503238846347, 414.7439135538566], "p3": [230.30503238846347, 440.82858679425135], "p4": [77.39676085759493, 449.3585409194425]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": false, "content": [{"bbox": null, "direction": null, "text": "10A", "score": null}], "bbox": {"p1": [77.39676085759493, 449.3585409194425], "p2": [230.30503238846347, 440.82858679425135], "p3": [233.63323777486858, 481.06407987402326], "p4": [79.33934429206313, 490.71833371718867]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": false, "content": [{"bbox": null, "direction": null, "text": "Delayed accessory-110-volt power point.\nRadio", "score": null}], "bbox": {"p1": [230.30503238846347, 440.82858679425135], "p2": [514.9845891262198, 427.6857014956445], "p3": [518.4981026397331, 466.06407987402235], "p4": [233.63323777486858, 481.06407987402326]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": false, "content": [{"bbox": null, "direction": null, "text": "Traliter brake control", "score": null}], "bbox": {"p1": [230.30503238846347, 414.7439135538566], "p2": [510.3899945316257, 403.2262420361849], "p3": [514.9845891262198, 427.6857014956445], "p4": [230.30503238846347, 440.82858679425135]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 9, "header": false, "content": [{"bbox": null, "direction": null, "text": "Restraint control module,Occupant classi-\nfication system module", "score": null}], "bbox": {"p1": [230.30503238846347, 373.8015597672247], "p2": [504.84945399108483, 364.71272852267157], "p3": [510.3899945316257, 403.2262420361849], "p4": [230.30503238846347, 414.7439135538566]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 10, "header": false, "content": [{"bbox": null, "direction": null, "text": "Hill descent switch(SVT Raptor)", "score": null}], "bbox": {"p1": [230.30503238846347, 348.3613771713203], "p2": [502.55288518508223, 340.38330475928524], "p3": [504.84945399108483, 364.71272852267157], "p4": [230.30503238846347, 373.8015597672247]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 11, "header": false, "content": [{"bbox": null, "direction": null, "text": "Reverse sensing systerm,4×4 switch, Rear\nvideo,Off-road indicator(SVT Raptor),\nFront video(SVT Raptor).Camera splice\nmodule(SVT Raptor)", "score": null}], "bbox": {"p1": [225.2005979714184, 275.287837187491], "p2": [497.9575620991932, 275.287837187491], "p3": [502.55288518508223, 340.38330475928524], "p4": [230.30503238846347, 348.3613771713203]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 12, "header": false, "content": [{"bbox": null, "direction": null, "text": "Rear heated seats", "score": null}], "bbox": {"p1": [225.2005979714184, 250.7377131464218], "p2": [496.47107561270604, 252.43657145674783], "p3": [497.9575620991932, 275.287837187491], "p4": [225.2005979714184, 275.287837187491]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 13, "header": true, "content": [{"bbox": null, "direction": null, "text": "Protected components", "score": null}], "bbox": {"p1": [225.2005979714184, 224.25628700766356], "p2": [495.4910032846915, 227.17993814560305], "p3": [496.47107561270604, 252.43657145674783], "p4": [225.2005979714184, 250.7377131464218]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 14, "header": true, "content": [{"bbox": null, "direction": null, "text": "#lay number", "score": null}], "bbox": {"p1": [-2.4116573333740234, 232.97396850585938], "p2": [72.5372617015567, 226.38375854492188], "p3": [74.8729000739293, 252.43657145674783], "p4": [7.62939453125e-06, 259.9320068359375]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 15, "header": false, "content": [{"bbox": null, "direction": null, "text": "33", "score": null}], "bbox": {"p1": [-1.9719314575195312, 257.52276611328125], "p2": [72.5372617015567, 252.43657145674783], "p3": [74.8729000739293, 279.22190748488924], "p4": [0.0, 283.51397705078125]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 16, "header": false, "content": [{"bbox": null, "direction": null, "text": "34", "score": null}], "bbox": {"p1": [-3.097763776779175, 283.51397705078125], "p2": [72.5372617015567, 279.22190748488924], "p3": [77.39676085759493, 353.19541287781897], "p4": [0.0, 356.7915863387618]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 17, "header": false, "content": [{"bbox": null, "direction": null, "text": "35", "score": null}], "bbox": {"p1": [0.0, 356.7915863387618], "p2": [74.8729000739293, 353.195412877819], "p3": [77.39676085759493, 380.84502740592933], "p4": [0.0, 380.84502740592933]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 18, "header": false, "content": [{"bbox": null, "direction": null, "text": "36", "score": null}], "bbox": {"p1": [0.0, 380.84502740592933], "p2": [77.39676085759493, 380.84502740592933], "p3": [79.33934429206313, 421.19921500915825], "p4": [0.0, 423.54836449287404]}, "lloc": {"start_row": 4, "end_row": 4, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 19, "header": false, "content": [{"bbox": null, "direction": null, "text": "37", "score": null}], "bbox": {"p1": [0.0, 423.54836449287404], "p2": [79.33934429206313, 421.19921500915825], "p3": [77.39676085759493, 449.3585409194425], "p4": [0.8253268596613452, 449.3585409194425]}, "lloc": {"start_row": 5, "end_row": 5, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 20, "header": false, "content": [{"bbox": null, "direction": null, "text": "38", "score": null}], "bbox": {"p1": [0.8253268596613452, 449.3585409194425], "p2": [77.39676085759493, 449.3585409194425], "p3": [79.33934429206313, 490.71833371718867], "p4": [0.9433418323615115, 490.98079692530655]}, "lloc": {"start_row": 6, "end_row": 6, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}