{"table_ind": "3076936749", "image_path": "3076936749.jpg", "type": 1, "cells": [{"cell_ind": 0, "header": true, "content": [{"bbox": null, "direction": null, "text": "Types of operands used", "score": null}], "bbox": {"p1": [290.94730376510404, 209.36231884057997], "p2": [495.4545454545453, 205.0], "p3": [495.4545454545453, 248.3804492774399], "p4": [290.94730376510404, 248.3804492774399]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": true, "content": [{"bbox": null, "direction": null, "text": "Addressing modes used for\neach operand", "score": null}], "bbox": {"p1": [495.4545454545453, 205.0], "p2": [739.1549815498156, 196.89298892988973], "p3": [740.1029411764703, 239.5294117647063], "p4": [495.4545454545453, 248.3804492774399]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": true, "content": [{"bbox": null, "direction": null, "text": "Assembly Language\nProgram", "score": null}], "bbox": {"p1": [124.33333333333333, 208.0], "p2": [290.94730376510404, 209.36231884057997], "p3": [290.94730376510404, 248.3804492774399], "p4": [124.33333333333333, 248.3804492774399]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": true, "content": [{"bbox": null, "direction": null, "text": "Line\n#", "score": null}], "bbox": {"p1": [70.0, 208.0], "p2": [124.33333333333333, 208.0], "p3": [124.33333333333333, 248.3804492774399], "p4": [69.0, 248.3804492774399]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [{"bbox": null, "direction": null, "text": "mov [num], eax\nmov eax, 4\npush ex\nmov ecx, num", "score": null}], "bbox": {"p1": [124.33333333333333, 248.3804492774399], "p2": [290.94730376510404, 248.3804492774399], "p3": [290.94730376510404, 494.43347639485], "p4": [114.0, 495.0]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": false, "content": [{"bbox": null, "direction": null, "text": "[num] => gereral\neax => reg###fer\neax =>reg###\n4 => gereral\necx => reg###\neax => reglster\nnum => gereral", "score": null}], "bbox": {"p1": [290.94730376510404, 248.3804492774399], "p2": [495.4545454545453, 248.3804492774399], "p3": [504.13733905579375, 493.0], "p4": [290.94730376510404, 494.43347639485]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": false, "content": [{"bbox": null, "direction": null, "text": "9.\n10.\n12.\n14.", "score": null}], "bbox": {"p1": [69.0, 248.3804492774399], "p2": [124.33333333333333, 248.3804492774399], "p3": [114.0, 495.0], "p4": [55.57081545064375, 496.5751072961375]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": false, "content": [{"bbox": null, "direction": null, "text": "[num]=indlrect addre###\neax=dlrect reglster\naddreahng\neax=dlrect regsteR\n4=>dlrect addre###\necx=dlr###ct reglsteR\nad###\necx=dlrect reglster addre###\nnum=dlrect addre###", "score": null}], "bbox": {"p1": [495.4545454545453, 248.3804492774399], "p2": [740.1029411764703, 239.5294117647063], "p3": [754.2110091743125, 490.2477064220184], "p4": [504.13733905579375, 493.0]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 3, "end_col": 3}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}